# Git
.git
.gitignore
.gitattributes

# Docker
docker-compose*.yml
Dockerfile*
.dockerignore

# Documentation
README.md
CHANGELOG.md
*.md

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Node
node_modules
npm-debug.log
yarn-error.log

# Laravel
/vendor
.env
.env.backup
.env.production
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json

# Storage
/storage/*.key
/storage/app/public
/storage/framework/cache/data
/storage/framework/sessions
/storage/framework/testing
/storage/framework/views
/storage/logs

# Public
/public/hot
/public/storage
/public/mix-manifest.json

# Build artifacts
/public/build
/public/css
/public/js

# Testing
/tests
phpunit.xml
.phpunit.cache

# Development tools
.php_cs.cache
.php-cs-fixer.cache

# Logs
*.log

# Temporary files
*.tmp
*.temp

# Database
*.sqlite
*.db

# Backup files
*.bak
*.backup

# Local development
docker-dev.sh
DOCKER-*.md
coolify.log
