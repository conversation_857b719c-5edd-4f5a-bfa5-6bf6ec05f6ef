# Git
.git
.gitignore
.gitattributes

# Docker development files (keep Dockerfile for Coolify)
docker-compose*.yml
.dockerignore

# Documentation
README.md
CHANGELOG.md
*.md
DOCKER-*.md

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Node
node_modules
npm-debug.log
yarn-error.log
.npm

# Laravel
/vendor
.env*
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json

# Storage (keep structure but exclude content)
/storage/*.key
/storage/app/*
!/storage/app/.gitignore
!/storage/app/public/.gitignore
/storage/framework/cache/data/*
!/storage/framework/cache/data/.gitignore
/storage/framework/sessions/*
!/storage/framework/sessions/.gitignore
/storage/framework/testing/*
!/storage/framework/testing/.gitignore
/storage/framework/views/*
!/storage/framework/views/.gitignore
/storage/logs/*
!/storage/logs/.gitignore

# Public build artifacts
/public/hot
/public/storage
/public/mix-manifest.json
/public/build
/public/css
/public/js

# Testing
/tests
phpunit.xml
.phpunit.cache

# Development tools
.php_cs.cache
.php-cs-fixer.cache

# Logs
*.log
coolify.log

# Temporary files
*.tmp
*.temp

# Database
*.sqlite
*.db

# Backup files
*.bak
*.backup

# Local development
docker-dev.sh
docker/
