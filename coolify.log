Starting deployment of taimoor-shan/immo212:features/rentals to localhost.
2025-Jul-27 02:19:05.882626
Preparing container with helper image: ghcr.io/coollabsio/coolify-helper:1.0.8.
2025-Jul-27 02:19:05.978785
[CMD]: docker stop --time=30 nco4cg0oo8go8og80c0wkg4o
2025-Jul-27 02:19:05.978785
Error response from daemon: No such container: nco4cg0oo8go8og80c0wkg4o
2025-Jul-27 02:19:06.072364
[CMD]: docker rm -f nco4cg0oo8go8og80c0wkg4o
2025-Jul-27 02:19:06.072364
Error response from daemon: No such container: nco4cg0oo8go8og80c0wkg4o
2025-Jul-27 02:19:06.218758
[CMD]: docker run -d --network coolify --name nco4cg0oo8go8og80c0wkg4o --rm -v /var/run/docker.sock:/var/run/docker.sock ghcr.io/coollabsio/coolify-helper:1.0.8
2025-Jul-27 02:19:06.218758
0f94cafd94383c2276b4fa84822c87471fa27a0332de90667fc2a668e0704037
2025-Jul-27 02:19:08.038190
[CMD]: docker exec nco4cg0oo8go8og80c0wkg4o bash -c 'GIT_SSH_COMMAND="ssh -o ConnectTimeout=30 -p 22 -o Port=22 -o LogLevel=ERROR -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git ls-remote https://x-access-token:<REDACTED>@github.com/taimoor-shan/immo212.git features/rentals'
2025-Jul-27 02:19:08.038190
98de18cabf09af55bfa61ea711c866ec36fe71d1	refs/heads/features/rentals
2025-Jul-27 02:19:08.222070
Image not found (s888kgk480ww8s0w88o0gwso:98de18cabf09af55bfa61ea711c866ec36fe71d1). Building new image.
2025-Jul-27 02:19:08.474900
----------------------------------------
2025-Jul-27 02:19:08.477193
Importing taimoor-shan/immo212:features/rentals (commit sha HEAD) to /artifacts/nco4cg0oo8go8og80c0wkg4o.
2025-Jul-27 02:19:08.713727
[CMD]: docker exec nco4cg0oo8go8og80c0wkg4o bash -c 'git clone -b "features/rentals" https://x-access-token:<REDACTED>@github.com/taimoor-shan/immo212.git /artifacts/nco4cg0oo8go8og80c0wkg4o && cd /artifacts/nco4cg0oo8go8og80c0wkg4o && GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git submodule update --init --recursive && cd /artifacts/nco4cg0oo8go8og80c0wkg4o && GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git lfs pull'
2025-Jul-27 02:19:08.713727
Cloning into '/artifacts/nco4cg0oo8go8og80c0wkg4o'...
2025-Jul-27 02:19:13.915738
Updating files:  37% (4231/11264)
2025-Jul-27 02:19:13.926185
Updating files:  38% (4281/11264)
2025-Jul-27 02:19:13.945443
Updating files:  39% (4393/11264)
2025-Jul-27 02:19:13.962128
Updating files:  40% (4506/11264)
2025-Jul-27 02:19:13.978712
Updating files:  41% (4619/11264)
2025-Jul-27 02:19:13.993812
Updating files:  42% (4731/11264)
2025-Jul-27 02:19:14.009805
Updating files:  43% (4844/11264)
2025-Jul-27 02:19:14.025394
Updating files:  44% (4957/11264)
2025-Jul-27 02:19:14.039921
Updating files:  45% (5069/11264)
2025-Jul-27 02:19:14.056614
Updating files:  46% (5182/11264)
2025-Jul-27 02:19:14.072160
Updating files:  47% (5295/11264)
2025-Jul-27 02:19:14.087588
Updating files:  48% (5407/11264)
2025-Jul-27 02:19:14.104861
Updating files:  49% (5520/11264)
2025-Jul-27 02:19:14.122808
Updating files:  50% (5632/11264)
2025-Jul-27 02:19:14.142202
Updating files:  51% (5745/11264)
2025-Jul-27 02:19:14.161306
Updating files:  52% (5858/11264)
2025-Jul-27 02:19:14.178687
Updating files:  53% (5970/11264)
2025-Jul-27 02:19:14.196051
Updating files:  54% (6083/11264)
2025-Jul-27 02:19:14.213096
Updating files:  55% (6196/11264)
2025-Jul-27 02:19:14.231297
Updating files:  56% (6308/11264)
2025-Jul-27 02:19:14.247190
Updating files:  57% (6421/11264)
2025-Jul-27 02:19:14.267023
Updating files:  58% (6534/11264)
2025-Jul-27 02:19:14.284341
Updating files:  59% (6646/11264)
2025-Jul-27 02:19:14.303122
Updating files:  60% (6759/11264)
2025-Jul-27 02:19:14.319396
Updating files:  61% (6872/11264)
2025-Jul-27 02:19:14.338379
Updating files:  62% (6984/11264)
2025-Jul-27 02:19:14.355579
Updating files:  63% (7097/11264)
2025-Jul-27 02:19:14.373843
Updating files:  64% (7209/11264)
2025-Jul-27 02:19:14.399194
Updating files:  65% (7322/11264)
2025-Jul-27 02:19:14.418802
Updating files:  66% (7435/11264)
2025-Jul-27 02:19:14.438758
Updating files:  67% (7547/11264)
2025-Jul-27 02:19:14.461512
Updating files:  68% (7660/11264)
2025-Jul-27 02:19:14.487394
Updating files:  69% (7773/11264)
2025-Jul-27 02:19:14.509129
Updating files:  70% (7885/11264)
2025-Jul-27 02:19:14.530409
Updating files:  71% (7998/11264)
2025-Jul-27 02:19:14.554172
Updating files:  72% (8111/11264)
2025-Jul-27 02:19:14.579572
Updating files:  73% (8223/11264)
2025-Jul-27 02:19:14.603005
Updating files:  74% (8336/11264)
2025-Jul-27 02:19:14.626219
Updating files:  75% (8448/11264)
2025-Jul-27 02:19:14.648490
Updating files:  76% (8561/11264)
2025-Jul-27 02:19:14.672349
Updating files:  77% (8674/11264)
2025-Jul-27 02:19:14.693174
Updating files:  78% (8786/11264)
2025-Jul-27 02:19:14.715169
Updating files:  79% (8899/11264)
2025-Jul-27 02:19:14.739295
Updating files:  80% (9012/11264)
2025-Jul-27 02:19:14.760214
Updating files:  81% (9124/11264)
2025-Jul-27 02:19:14.783230
Updating files:  82% (9237/11264)
2025-Jul-27 02:19:14.803268
Updating files:  83% (9350/11264)
2025-Jul-27 02:19:14.823914
Updating files:  84% (9462/11264)
2025-Jul-27 02:19:14.853299
Updating files:  85% (9575/11264)
2025-Jul-27 02:19:14.910216
Updating files:  86% (9688/11264)
2025-Jul-27 02:19:14.917646
Updating files:  86% (9700/11264)
2025-Jul-27 02:19:14.957799
Updating files:  87% (9800/11264)
2025-Jul-27 02:19:14.980824
Updating files:  88% (9913/11264)
2025-Jul-27 02:19:15.019917
Updating files:  89% (10025/11264)
2025-Jul-27 02:19:15.045180
Updating files:  90% (10138/11264)
2025-Jul-27 02:19:15.088015
Updating files:  91% (10251/11264)
2025-Jul-27 02:19:15.154157
Updating files:  92% (10363/11264)
2025-Jul-27 02:19:15.171081
Updating files:  93% (10476/11264)
2025-Jul-27 02:19:15.197638
Updating files:  94% (10589/11264)
2025-Jul-27 02:19:15.256659
Updating files:  95% (10701/11264)
2025-Jul-27 02:19:15.331123
Updating files:  96% (10814/11264)
2025-Jul-27 02:19:15.377583
Updating files:  97% (10927/11264)
2025-Jul-27 02:19:15.422179
Updating files:  98% (11039/11264)
2025-Jul-27 02:19:15.446801
Updating files:  99% (11152/11264)
2025-Jul-27 02:19:15.484759
Updating files: 100% (11264/11264)
Updating files: 100% (11264/11264), done.
2025-Jul-27 02:19:16.171358
[CMD]: docker exec nco4cg0oo8go8og80c0wkg4o bash -c 'cd /artifacts/nco4cg0oo8go8og80c0wkg4o && git log -1 98de18cabf09af55bfa61ea711c866ec36fe71d1 --pretty=%B'
2025-Jul-27 02:19:16.171358
getting ready for Cloud Panel
2025-Jul-27 02:19:16.549250
Generating nixpacks configuration with: nixpacks plan -f toml  /artifacts/nco4cg0oo8go8og80c0wkg4o
2025-Jul-27 02:19:18.422296
[CMD]: docker exec nco4cg0oo8go8og80c0wkg4o bash -c 'nixpacks plan -f toml  /artifacts/nco4cg0oo8go8og80c0wkg4o'
2025-Jul-27 02:19:18.422296
providers = []
2025-Jul-27 02:19:18.422296
buildImage = 'ghcr.io/railwayapp/nixpacks:ubuntu-**********'
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
[variables]
2025-Jul-27 02:19:18.422296
IS_LARAVEL = 'yes'
2025-Jul-27 02:19:18.422296
NIXPACKS_METADATA = 'php'
2025-Jul-27 02:19:18.422296
NIXPACKS_PHP_ROOT_DIR = '/app/public'
2025-Jul-27 02:19:18.422296
PORT = '80'
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
[staticAssets]
2025-Jul-27 02:19:18.422296
"nginx.template.conf" = '''
2025-Jul-27 02:19:18.422296
worker_processes 5;
2025-Jul-27 02:19:18.422296
daemon off;
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
worker_rlimit_nofile 8192;
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
events {
2025-Jul-27 02:19:18.422296
worker_connections  4096;  # Default: 1024
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
http {
2025-Jul-27 02:19:18.422296
include    $!{nginx}/conf/mime.types;
2025-Jul-27 02:19:18.422296
index    index.html index.htm index.php;
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
default_type application/octet-stream;
2025-Jul-27 02:19:18.422296
log_format   main '$remote_addr - $remote_user [$time_local]  $status '
2025-Jul-27 02:19:18.422296
'"$request" $body_bytes_sent "$http_referer" '
2025-Jul-27 02:19:18.422296
'"$http_user_agent" "$http_x_forwarded_for"';
2025-Jul-27 02:19:18.422296
access_log /dev/stdout;
2025-Jul-27 02:19:18.422296
error_log /dev/stdout;
2025-Jul-27 02:19:18.422296
sendfile     on;
2025-Jul-27 02:19:18.422296
tcp_nopush   on;
2025-Jul-27 02:19:18.422296
server_names_hash_bucket_size 128; # this seems to be required for some vhosts
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
server {
2025-Jul-27 02:19:18.422296
listen ${PORT};
2025-Jul-27 02:19:18.422296
listen [::]:${PORT};
2025-Jul-27 02:19:18.422296
server_name localhost;
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
$if(NIXPACKS_PHP_ROOT_DIR) (
2025-Jul-27 02:19:18.422296
root ${NIXPACKS_PHP_ROOT_DIR};
2025-Jul-27 02:19:18.422296
) else (
2025-Jul-27 02:19:18.422296
root /app;
2025-Jul-27 02:19:18.422296
)
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
add_header X-Frame-Options "SAMEORIGIN";
2025-Jul-27 02:19:18.422296
add_header X-Content-Type-Options "nosniff";
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
index index.php;
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
charset utf-8;
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
$if(IS_LARAVEL) (
2025-Jul-27 02:19:18.422296
location / {
2025-Jul-27 02:19:18.422296
try_files $uri $uri/ /index.php?$query_string;
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
) else ()
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
$if(NIXPACKS_PHP_FALLBACK_PATH) (
2025-Jul-27 02:19:18.422296
location / {
2025-Jul-27 02:19:18.422296
try_files $uri $uri/ ${NIXPACKS_PHP_FALLBACK_PATH}?$query_string;
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
) else ()
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
location = /favicon.ico { access_log off; log_not_found off; }
2025-Jul-27 02:19:18.422296
location = /robots.txt  { access_log off; log_not_found off; }
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
$if(IS_LARAVEL) (
2025-Jul-27 02:19:18.422296
error_page 404 /index.php;
2025-Jul-27 02:19:18.422296
) else ()
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
location ~ \.php$ {
2025-Jul-27 02:19:18.422296
fastcgi_pass 127.0.0.1:9000;
2025-Jul-27 02:19:18.422296
fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
2025-Jul-27 02:19:18.422296
include $!{nginx}/conf/fastcgi_params;
2025-Jul-27 02:19:18.422296
include $!{nginx}/conf/fastcgi.conf;
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
location ~ /\.(?!well-known).* {
2025-Jul-27 02:19:18.422296
deny all;
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
}'''
2025-Jul-27 02:19:18.422296
"php-fpm.conf" = '''
2025-Jul-27 02:19:18.422296
[www]
2025-Jul-27 02:19:18.422296
listen = 127.0.0.1:9000
2025-Jul-27 02:19:18.422296
user = nobody
2025-Jul-27 02:19:18.422296
pm = dynamic
2025-Jul-27 02:19:18.422296
pm.max_children = 50
2025-Jul-27 02:19:18.422296
pm.min_spare_servers = 4
2025-Jul-27 02:19:18.422296
pm.max_spare_servers = 32
2025-Jul-27 02:19:18.422296
pm.start_servers = 18
2025-Jul-27 02:19:18.422296
clear_env = no
2025-Jul-27 02:19:18.422296
catch_workers_output = yes
2025-Jul-27 02:19:18.422296
'''
2025-Jul-27 02:19:18.422296
"scripts/config/template.mjs" = '''
2025-Jul-27 02:19:18.422296
import { readFile, writeFile } from "fs/promises";
2025-Jul-27 02:19:18.422296
import { getNixPath } from "../util/nix.mjs";
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
const replaceStr = input =>
2025-Jul-27 02:19:18.422296
input
2025-Jul-27 02:19:18.422296
// If statements
2025-Jul-27 02:19:18.422296
.replaceAll(/\$if\s*\((\w+)\)\s*\(([^]*?)\)\s*else\s*\(([^]*?)\)/gm,
2025-Jul-27 02:19:18.422296
(_all, condition, value, otherwise) =>
2025-Jul-27 02:19:18.422296
process.env[condition] ? replaceStr(value) : replaceStr(otherwise)
2025-Jul-27 02:19:18.422296
)
2025-Jul-27 02:19:18.422296
// Variables
2025-Jul-27 02:19:18.422296
.replaceAll(/\${(\w+)}/g,
2025-Jul-27 02:19:18.422296
(_all, name) => process.env[name]
2025-Jul-27 02:19:18.422296
)
2025-Jul-27 02:19:18.422296
// Nix paths
2025-Jul-27 02:19:18.422296
.replaceAll(/\$!{(\w+)}/g,
2025-Jul-27 02:19:18.422296
(_all, exe) => getNixPath(exe)
2025-Jul-27 02:19:18.422296
)
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
export async function compileTemplate(infile, outfile) {
2025-Jul-27 02:19:18.422296
await writeFile(outfile,
2025-Jul-27 02:19:18.422296
replaceStr(await readFile(infile, { encoding: 'utf8' })),
2025-Jul-27 02:19:18.422296
{ encoding: 'utf8' })
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
'''
2025-Jul-27 02:19:18.422296
"scripts/prestart.mjs" = '''
2025-Jul-27 02:19:18.422296
#!/usr/bin/env node
2025-Jul-27 02:19:18.422296
import { compileTemplate } from "./config/template.mjs";
2025-Jul-27 02:19:18.422296
import { e } from "./util/cmd.mjs";
2025-Jul-27 02:19:18.422296
import { checkEnvErrors, isLaravel } from "./util/laravel.mjs";
2025-Jul-27 02:19:18.422296
import Logger from "./util/logger.mjs";
2025-Jul-27 02:19:18.422296
import { access, constants } from 'node:fs/promises'
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
const prestartLogger = new Logger('prestart');
2025-Jul-27 02:19:18.422296
const serverLogger = new Logger('server');
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
if (process.argv.length != 4) {
2025-Jul-27 02:19:18.422296
prestartLogger.error(`Usage: ${process.argv[1]} <config-file> <output-file>`)
2025-Jul-27 02:19:18.422296
process.exit(1);
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
await Promise.all([
2025-Jul-27 02:19:18.422296
isLaravel() ? checkEnvErrors('/app') : Promise.resolve(),
2025-Jul-27 02:19:18.422296
access('/app/storage', constants.R_OK)
2025-Jul-27 02:19:18.422296
.then(() => e('chmod -R ugo+rw /app/storage'))
2025-Jul-27 02:19:18.422296
.catch(() => {}),
2025-Jul-27 02:19:18.422296
compileTemplate(process.argv[2], process.argv[3])
2025-Jul-27 02:19:18.422296
]).catch(err => prestartLogger.error(err));
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
serverLogger.info(`Server starting on port ${process.env.PORT}`)
2025-Jul-27 02:19:18.422296
'''
2025-Jul-27 02:19:18.422296
"scripts/util/cmd.mjs" = '''
2025-Jul-27 02:19:18.422296
import { execSync } from "child_process";
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
export const e = cmd => execSync(cmd).toString().replace('\n', '');'''
2025-Jul-27 02:19:18.422296
"scripts/util/laravel.mjs" = '''
2025-Jul-27 02:19:18.422296
import Logger from "./logger.mjs"
2025-Jul-27 02:19:18.422296
import * as fs from 'node:fs/promises'
2025-Jul-27 02:19:18.422296
import * as path from 'node:path'
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
const variableHints = {
2025-Jul-27 02:19:18.422296
'APP_ENV': 'You should probably set this to `production`.'
2025-Jul-27 02:19:18.422296
};
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
const logger = new Logger('laravel');
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
export const isLaravel = () => process.env['IS_LARAVEL'] != null;
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
function checkVariable(name) {
2025-Jul-27 02:19:18.422296
if (!process.env[name]) {
2025-Jul-27 02:19:18.422296
let hint =
2025-Jul-27 02:19:18.422296
`Your app configuration references the ${name} environment variable, but it is not set.`
2025-Jul-27 02:19:18.422296
+ (variableHints[name] ?? '');
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
logger.warn(hint);
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
export async function checkEnvErrors(srcdir) {
2025-Jul-27 02:19:18.422296
const envRegex = /env\(["']([^,]*)["']\)/g;
2025-Jul-27 02:19:18.422296
const configDir = path.join(srcdir, 'config');
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
const config =
2025-Jul-27 02:19:18.422296
(await Promise.all(
2025-Jul-27 02:19:18.422296
(await fs.readdir(configDir))
2025-Jul-27 02:19:18.422296
.filter(fileName => fileName.endsWith('.php'))
2025-Jul-27 02:19:18.422296
.map(fileName => fs.readFile(path.join(configDir, fileName)))
2025-Jul-27 02:19:18.422296
)).join('');
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
for (const match of config.matchAll(envRegex)) {
2025-Jul-27 02:19:18.422296
if (match[1] != 'APP_KEY') checkVariable(match[1]);
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
if (!process.env.APP_KEY) {
2025-Jul-27 02:19:18.422296
logger.warn('Your app key is not set! Please set a random 32-character string in your APP_KEY environment variable. This can be easily generated with `openssl rand -hex 16`.');
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
'''
2025-Jul-27 02:19:18.422296
"scripts/util/logger.mjs" = '''
2025-Jul-27 02:19:18.422296
export default class Logger {
2025-Jul-27 02:19:18.422296
/** @type string */
2025-Jul-27 02:19:18.422296
#tag;
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
/**
2025-Jul-27 02:19:18.422296
* @param {string} tag
2025-Jul-27 02:19:18.422296
*/
2025-Jul-27 02:19:18.422296
constructor(tag) {
2025-Jul-27 02:19:18.422296
this.#tag = tag
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
#log(color, messageType, message, fn = console.log) {
2025-Jul-27 02:19:18.422296
fn(`\x1b[${color}m[${this.#tag}:${messageType}]\x1b[0m ${message}`)
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
info(message) {
2025-Jul-27 02:19:18.422296
this.#log(34, 'info', message)
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
warn(message) {
2025-Jul-27 02:19:18.422296
this.#log(35, 'warn', message, console.warn)
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
error(message) {
2025-Jul-27 02:19:18.422296
this.#log(31, 'error', message, console.error)
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
}
2025-Jul-27 02:19:18.422296
'''
2025-Jul-27 02:19:18.422296
"scripts/util/nix.mjs" = '''
2025-Jul-27 02:19:18.422296
import { e } from "./cmd.mjs";
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
export const getNixPath = (exe) => e(`nix-store -q ${e(`which ${exe}`)}`);
2025-Jul-27 02:19:18.422296
'''
2025-Jul-27 02:19:18.422296
[phases.build]
2025-Jul-27 02:19:18.422296
dependsOn = ['install']
2025-Jul-27 02:19:18.422296
cmds = ['npm run prod']
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
[phases.install]
2025-Jul-27 02:19:18.422296
dependsOn = ['setup']
2025-Jul-27 02:19:18.422296
cmds = [
2025-Jul-27 02:19:18.422296
'mkdir -p /var/log/nginx && mkdir -p /var/cache/nginx',
2025-Jul-27 02:19:18.422296
'composer install --ignore-platform-reqs',
2025-Jul-27 02:19:18.422296
'npm ci',
2025-Jul-27 02:19:18.422296
]
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
[phases.setup]
2025-Jul-27 02:19:18.422296
nixPkgs = [
2025-Jul-27 02:19:18.422296
'(php.withExtensions (pe: pe.enabled ++ [pe.all.curl pe.all.gd pe.all.mbstring pe.all.openssl pe.all.pdo pe.all.tokenizer pe.all.xml pe.all.zip]))',
2025-Jul-27 02:19:18.422296
'nginx',
2025-Jul-27 02:19:18.422296
'libmysqlclient',
2025-Jul-27 02:19:18.422296
'phpPackages.composer',
2025-Jul-27 02:19:18.422296
'nodejs_18',
2025-Jul-27 02:19:18.422296
'npm-9_x',
2025-Jul-27 02:19:18.422296
'phpExtensions.curl',
2025-Jul-27 02:19:18.422296
'phpExtensions.gd',
2025-Jul-27 02:19:18.422296
'phpExtensions.mbstring',
2025-Jul-27 02:19:18.422296
'phpExtensions.openssl',
2025-Jul-27 02:19:18.422296
'phpExtensions.pdo',
2025-Jul-27 02:19:18.422296
'phpExtensions.tokenizer',
2025-Jul-27 02:19:18.422296
'phpExtensions.xml',
2025-Jul-27 02:19:18.422296
'phpExtensions.zip',
2025-Jul-27 02:19:18.422296
]
2025-Jul-27 02:19:18.422296
nixLibs = [
2025-Jul-27 02:19:18.422296
'phpExtensions.curl',
2025-Jul-27 02:19:18.422296
'phpExtensions.gd',
2025-Jul-27 02:19:18.422296
'phpExtensions.mbstring',
2025-Jul-27 02:19:18.422296
'phpExtensions.openssl',
2025-Jul-27 02:19:18.422296
'phpExtensions.pdo',
2025-Jul-27 02:19:18.422296
'phpExtensions.tokenizer',
2025-Jul-27 02:19:18.422296
'phpExtensions.xml',
2025-Jul-27 02:19:18.422296
'phpExtensions.zip',
2025-Jul-27 02:19:18.422296
'libmysqlclient',
2025-Jul-27 02:19:18.422296
]
2025-Jul-27 02:19:18.422296
nixOverlays = ['https://github.com/railwayapp/nix-npm-overlay/archive/main.tar.gz']
2025-Jul-27 02:19:18.422296
nixpkgsArchive = 'e24b4c09e963677b1beea49d411cd315a024ad3a'
2025-Jul-27 02:19:18.422296
2025-Jul-27 02:19:18.422296
[start]
2025-Jul-27 02:19:18.422296
cmd = 'php-fpm -y /assets/php-fpm.conf & nginx -c /app/nginx.conf'
2025-Jul-27 02:19:18.442226
2025-Jul-27 02:19:20.000071
[CMD]: docker exec nco4cg0oo8go8og80c0wkg4o bash -c 'nixpacks detect /artifacts/nco4cg0oo8go8og80c0wkg4o'
2025-Jul-27 02:19:20.000071
php
2025-Jul-27 02:19:20.012014
Found application type: php.
2025-Jul-27 02:19:20.029070
If you need further customization, please check the documentation of Nixpacks: https://nixpacks.com/docs/providers/php
2025-Jul-27 02:19:20.153479
Final Nixpacks plan: {
2025-Jul-27 02:19:20.153479
"providers": [],
2025-Jul-27 02:19:20.153479
"buildImage": "ghcr.io\/railwayapp\/nixpacks:ubuntu-**********",
2025-Jul-27 02:19:20.153479
"variables": {
2025-Jul-27 02:19:20.153479
"IS_LARAVEL": "yes",
2025-Jul-27 02:19:20.153479
"NIXPACKS_METADATA": "php",
2025-Jul-27 02:19:20.153479
"NIXPACKS_PHP_ROOT_DIR": "\/app\/public",
2025-Jul-27 02:19:20.153479
"PORT": "80",
2025-Jul-27 02:19:20.153479
"SOURCE_COMMIT": "98de18cabf09af55bfa61ea711c866ec36fe71d1",
2025-Jul-27 02:19:20.153479
"NIXPACKS_PHP_FALLBACK_PATH": "\/index.php"
2025-Jul-27 02:19:20.153479
},
2025-Jul-27 02:19:20.153479
"staticAssets": {
2025-Jul-27 02:19:20.153479
"nginx.template.conf": "worker_processes 5;\ndaemon off;\n\nworker_rlimit_nofile 8192;\n\nevents {\n  worker_connections  4096;  # Default: 1024\n}\n\nhttp {\n    include    $!{nginx}\/conf\/mime.types;\n    index    index.html index.htm index.php;\n\n    default_type application\/octet-stream;\n    log_format   main '$remote_addr - $remote_user [$time_local]  $status '\n        '\"$request\" $body_bytes_sent \"$http_referer\" '\n        '\"$http_user_agent\" \"$http_x_forwarded_for\"';\n    access_log \/dev\/stdout;\n    error_log \/dev\/stdout;\n    sendfile     on;\n    tcp_nopush   on;\n    server_names_hash_bucket_size 128; # this seems to be required for some vhosts\n\n    server {\n        listen ${PORT};\n        listen [::]:${PORT};\n        server_name localhost;\n\n        $if(NIXPACKS_PHP_ROOT_DIR) (\n            root ${NIXPACKS_PHP_ROOT_DIR};\n        ) else (\n            root \/app;\n        )\n     \n        add_header X-Frame-Options \"SAMEORIGIN\";\n        add_header X-Content-Type-Options \"nosniff\";\n     \n        index index.php;\n     \n        charset utf-8;\n     \n        $if(IS_LARAVEL) (\n            location \/ {\n                try_files $uri $uri\/ \/index.php?$query_string;\n            }\n        ) else ()\n        \n        $if(NIXPACKS_PHP_FALLBACK_PATH) (\n          location \/ {\n            try_files $uri $uri\/ ${NIXPACKS_PHP_FALLBACK_PATH}?$query_string;\n          }\n        ) else ()\n     \n        location = \/favicon.ico { access_log off; log_not_found off; }\n        location = \/robots.txt  { access_log off; log_not_found off; }\n     \n        $if(IS_LARAVEL) (\n            error_page 404 \/index.php;\n        ) else ()\n     \n        location ~ \\.php$ {\n            fastcgi_pass 127.0.0.1:9000;\n            fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;\n            include $!{nginx}\/conf\/fastcgi_params;\n            include $!{nginx}\/conf\/fastcgi.conf;\n        }\n     \n        location ~ \/\\.(?!well-known).* {\n            deny all;\n        }\n    }\n}",
2025-Jul-27 02:19:20.153479
"php-fpm.conf": "[www]\nlisten = 127.0.0.1:9000\nuser = nobody\npm = dynamic\npm.max_children = 50\npm.min_spare_servers = 4\npm.max_spare_servers = 32\npm.start_servers = 18\nclear_env = no\ncatch_workers_output = yes\n",
2025-Jul-27 02:19:20.153479
"scripts\/config\/template.mjs": "import { readFile, writeFile } from \"fs\/promises\";\nimport { getNixPath } from \"..\/util\/nix.mjs\";\n\nconst replaceStr = input =>\n    input\n        \/\/ If statements\n        .replaceAll(\/\\$if\\s*\\((\\w+)\\)\\s*\\(([^]*?)\\)\\s*else\\s*\\(([^]*?)\\)\/gm,\n            (_all, condition, value, otherwise) =>\n                process.env[condition] ? replaceStr(value) : replaceStr(otherwise)\n        )\n        \/\/ Variables\n        .replaceAll(\/\\${(\\w+)}\/g,\n            (_all, name) => process.env[name]\n        )\n        \/\/ Nix paths\n        .replaceAll(\/\\$!{(\\w+)}\/g,\n            (_all, exe) => getNixPath(exe)\n        )\n\nexport async function compileTemplate(infile, outfile) {\n    await writeFile(outfile,\n        replaceStr(await readFile(infile, { encoding: 'utf8' })),\n        { encoding: 'utf8' })\n}\n",
2025-Jul-27 02:19:20.153479
"scripts\/prestart.mjs": "#!\/usr\/bin\/env node\nimport { compileTemplate } from \".\/config\/template.mjs\";\nimport { e } from \".\/util\/cmd.mjs\";\nimport { checkEnvErrors, isLaravel } from \".\/util\/laravel.mjs\";\nimport Logger from \".\/util\/logger.mjs\";\nimport { access, constants } from 'node:fs\/promises'\n\nconst prestartLogger = new Logger('prestart');\nconst serverLogger = new Logger('server');\n\nif (process.argv.length != 4) {\n    prestartLogger.error(`Usage: ${process.argv[1]} <config-file> <output-file>`)\n    process.exit(1);\n}\n\nawait Promise.all([\n    isLaravel() ? checkEnvErrors('\/app') : Promise.resolve(),\n    access('\/app\/storage', constants.R_OK)\n        .then(() => e('chmod -R ugo+rw \/app\/storage'))\n        .catch(() => {}),\n    compileTemplate(process.argv[2], process.argv[3])\n]).catch(err => prestartLogger.error(err));\n\nserverLogger.info(`Server starting on port ${process.env.PORT}`)\n",
2025-Jul-27 02:19:20.153479
"scripts\/util\/cmd.mjs": "import { execSync } from \"child_process\";\n\nexport const e = cmd => execSync(cmd).toString().replace('\\n', '');",
2025-Jul-27 02:19:20.153479
"scripts\/util\/laravel.mjs": "import Logger from \".\/logger.mjs\"\nimport * as fs from 'node:fs\/promises'\nimport * as path from 'node:path'\n\nconst variableHints = {\n    'APP_ENV': 'You should probably set this to `production`.'\n};\n\nconst logger = new Logger('laravel');\n\nexport const isLaravel = () => process.env['IS_LARAVEL'] != null;\n\nfunction checkVariable(name) {\n    if (!process.env[name]) {\n        let hint =\n            `Your app configuration references the ${name} environment variable, but it is not set.`\n            + (variableHints[name] ?? '');\n\n        logger.warn(hint);\n    }\n}\n\nexport async function checkEnvErrors(srcdir) {\n    const envRegex = \/env\\([\"']([^,]*)[\"']\\)\/g;\n    const configDir = path.join(srcdir, 'config');\n\n    const config =\n        (await Promise.all(\n            (await fs.readdir(configDir))\n                .filter(fileName => fileName.endsWith('.php'))\n                .map(fileName => fs.readFile(path.join(configDir, fileName)))\n        )).join('');\n\n    for (const match of config.matchAll(envRegex)) {\n        if (match[1] != 'APP_KEY') checkVariable(match[1]);\n    }\n\n    if (!process.env.APP_KEY) {\n        logger.warn('Your app key is not set! Please set a random 32-character string in your APP_KEY environment variable. This can be easily generated with `openssl rand -hex 16`.');\n    }\n}\n",
2025-Jul-27 02:19:20.153479
"scripts\/util\/logger.mjs": "export default class Logger {\n    \/** @type string *\/\n    #tag;\n\n    \/**\n    * @param {string} tag\n    *\/\n    constructor(tag) {\n        this.#tag = tag\n    }\n\n    #log(color, messageType, message, fn = console.log) {\n        fn(`\\x1b[${color}m[${this.#tag}:${messageType}]\\x1b[0m ${message}`)\n    }\n\n    info(message) {\n        this.#log(34, 'info', message)\n    }\n\n    warn(message) {\n        this.#log(35, 'warn', message, console.warn)\n    }\n\n    error(message) {\n        this.#log(31, 'error', message, console.error)\n    }\n}\n",
2025-Jul-27 02:19:20.153479
"scripts\/util\/nix.mjs": "import { e } from \".\/cmd.mjs\";\n\nexport const getNixPath = (exe) => e(`nix-store -q ${e(`which ${exe}`)}`);\n"
2025-Jul-27 02:19:20.153479
},
2025-Jul-27 02:19:20.153479
"phases": {
2025-Jul-27 02:19:20.153479
"build": {
2025-Jul-27 02:19:20.153479
"dependsOn": [
2025-Jul-27 02:19:20.153479
"install"
2025-Jul-27 02:19:20.153479
],
2025-Jul-27 02:19:20.153479
"cmds": [
2025-Jul-27 02:19:20.153479
"npm run prod"
2025-Jul-27 02:19:20.153479
]
2025-Jul-27 02:19:20.153479
},
2025-Jul-27 02:19:20.153479
"install": {
2025-Jul-27 02:19:20.153479
"dependsOn": [
2025-Jul-27 02:19:20.153479
"setup"
2025-Jul-27 02:19:20.153479
],
2025-Jul-27 02:19:20.153479
"cmds": [
2025-Jul-27 02:19:20.153479
"mkdir -p \/var\/log\/nginx && mkdir -p \/var\/cache\/nginx",
2025-Jul-27 02:19:20.153479
"composer install --ignore-platform-reqs",
2025-Jul-27 02:19:20.153479
"npm ci"
2025-Jul-27 02:19:20.153479
]
2025-Jul-27 02:19:20.153479
},
2025-Jul-27 02:19:20.153479
"setup": {
2025-Jul-27 02:19:20.153479
"nixPkgs": [
2025-Jul-27 02:19:20.153479
"(php.withExtensions (pe: pe.enabled ++ [pe.all.curl pe.all.gd pe.all.mbstring pe.all.openssl pe.all.pdo pe.all.tokenizer pe.all.xml pe.all.zip]))",
2025-Jul-27 02:19:20.153479
"nginx",
2025-Jul-27 02:19:20.153479
"libmysqlclient",
2025-Jul-27 02:19:20.153479
"phpPackages.composer",
2025-Jul-27 02:19:20.153479
"nodejs_18",
2025-Jul-27 02:19:20.153479
"npm-9_x",
2025-Jul-27 02:19:20.153479
"phpExtensions.curl",
2025-Jul-27 02:19:20.153479
"phpExtensions.gd",
2025-Jul-27 02:19:20.153479
"phpExtensions.mbstring",
2025-Jul-27 02:19:20.153479
"phpExtensions.openssl",
2025-Jul-27 02:19:20.153479
"phpExtensions.pdo",
2025-Jul-27 02:19:20.153479
"phpExtensions.tokenizer",
2025-Jul-27 02:19:20.153479
"phpExtensions.xml",
2025-Jul-27 02:19:20.153479
"phpExtensions.zip"
2025-Jul-27 02:19:20.153479
],
2025-Jul-27 02:19:20.153479
"nixLibs": [
2025-Jul-27 02:19:20.153479
"phpExtensions.curl",
2025-Jul-27 02:19:20.153479
"phpExtensions.gd",
2025-Jul-27 02:19:20.153479
"phpExtensions.mbstring",
2025-Jul-27 02:19:20.153479
"phpExtensions.openssl",
2025-Jul-27 02:19:20.153479
"phpExtensions.pdo",
2025-Jul-27 02:19:20.153479
"phpExtensions.tokenizer",
2025-Jul-27 02:19:20.153479
"phpExtensions.xml",
2025-Jul-27 02:19:20.153479
"phpExtensions.zip",
2025-Jul-27 02:19:20.153479
"libmysqlclient"
2025-Jul-27 02:19:20.153479
],
2025-Jul-27 02:19:20.153479
"nixOverlays": [
2025-Jul-27 02:19:20.153479
"https:\/\/github.com\/railwayapp\/nix-npm-overlay\/archive\/main.tar.gz"
2025-Jul-27 02:19:20.153479
],
2025-Jul-27 02:19:20.153479
"nixpkgsArchive": "e24b4c09e963677b1beea49d411cd315a024ad3a",
2025-Jul-27 02:19:20.153479
"aptPkgs": [
2025-Jul-27 02:19:20.153479
"curl",
2025-Jul-27 02:19:20.153479
"wget"
2025-Jul-27 02:19:20.153479
]
2025-Jul-27 02:19:20.153479
}
2025-Jul-27 02:19:20.153479
},
2025-Jul-27 02:19:20.153479
"start": {
2025-Jul-27 02:19:20.153479
"cmd": "php-fpm -y \/assets\/php-fpm.conf & nginx -c \/app\/nginx.conf"
2025-Jul-27 02:19:20.153479
}
2025-Jul-27 02:19:20.153479
}
2025-Jul-27 02:19:21.462524
----------------------------------------
2025-Jul-27 02:19:21.467835
Building docker image started.
2025-Jul-27 02:19:21.471714
To check the current progress, click on Show Debug Logs.
2025-Jul-27 02:19:22.367592
[CMD]: docker exec nco4cg0oo8go8og80c0wkg4o bash -c 'nixpacks build -c /artifacts/thegameplan.json --cache-key 's888kgk480ww8s0w88o0gwso' --no-error-without-start -n s888kgk480ww8s0w88o0gwso:98de18cabf09af55bfa61ea711c866ec36fe71d1 /artifacts/nco4cg0oo8go8og80c0wkg4o -o /artifacts/nco4cg0oo8go8og80c0wkg4o'
2025-Jul-27 02:19:22.367592
2025-Jul-27 02:19:22.367592
╔══════════════════════════════ Nixpacks v1.34.1 ══════════════════════════════╗
2025-Jul-27 02:19:22.367592
║ setup      │ (php.withExtensions (pe: pe.enabled ++ [pe.all.curl pe.all.gd   ║
2025-Jul-27 02:19:22.367592
║            │ pe.all.mbstring pe.all.openssl pe.all.pdo pe.all.tokenizer      ║
2025-Jul-27 02:19:22.367592
║            │ pe.all.xml pe.all.zip])), nginx, libmysqlclient,                ║
2025-Jul-27 02:19:22.367592
║            │ phpPackages.composer, nodejs_18, npm-9_x, phpExtensions.curl,   ║
2025-Jul-27 02:19:22.367592
║            │ phpExtensions.gd, phpExtensions.mbstring,                       ║
2025-Jul-27 02:19:22.367592
║            │ phpExtensions.openssl, phpExtensions.pdo,                       ║
2025-Jul-27 02:19:22.367592
║            │ phpExtensions.tokenizer, phpExtensions.xml, phpExtensions.zip,  ║
2025-Jul-27 02:19:22.367592
║            │ curl, wget                                                      ║
2025-Jul-27 02:19:22.367592
║──────────────────────────────────────────────────────────────────────────────║
2025-Jul-27 02:19:22.367592
║ install    │ mkdir -p /var/log/nginx && mkdir -p /var/cache/nginx            ║
2025-Jul-27 02:19:22.367592
║            │ composer install --ignore-platform-reqs                         ║
2025-Jul-27 02:19:22.367592
║            │ npm ci                                                          ║
2025-Jul-27 02:19:22.367592
║──────────────────────────────────────────────────────────────────────────────║
2025-Jul-27 02:19:22.367592
║ build      │ npm run prod                                                    ║
2025-Jul-27 02:19:22.367592
║──────────────────────────────────────────────────────────────────────────────║
2025-Jul-27 02:19:22.367592
║ start      │ php-fpm -y /assets/php-fpm.conf & nginx -c /app/nginx.conf      ║
2025-Jul-27 02:19:22.367592
╚══════════════════════════════════════════════════════════════════════════════╝
2025-Jul-27 02:19:22.378853
Saved output to:
2025-Jul-27 02:19:22.378853
/artifacts/nco4cg0oo8go8og80c0wkg4o
2025-Jul-27 02:19:22.753911
[CMD]: docker exec nco4cg0oo8go8og80c0wkg4o bash -c 'cat /artifacts/build.sh'
2025-Jul-27 02:19:22.753911
docker build --add-host coolify:******** --add-host coolify-db:******** --add-host coolify-realtime:******** --add-host coolify-redis:******** --network host -f /artifacts/nco4cg0oo8go8og80c0wkg4o/.nixpacks/Dockerfile --build-arg IS_LARAVEL='yes' --build-arg NIXPACKS_METADATA='php' --build-arg NIXPACKS_PHP_ROOT_DIR='/app/public' --build-arg PORT='80' --build-arg SOURCE_COMMIT='98de18cabf09af55bfa61ea711c866ec36fe71d1' --build-arg NIXPACKS_PHP_FALLBACK_PATH='/index.php' --build-arg 'COOLIFY_URL=http://s888kgk480ww8s0w88o0gwso.************.sslip.io' --build-arg 'COOLIFY_FQDN=s888kgk480ww8s0w88o0gwso.************.sslip.io' --build-arg 'COOLIFY_BRANCH=features/rentals' --build-arg 'COOLIFY_RESOURCE_UUID=s888kgk480ww8s0w88o0gwso' --build-arg 'COOLIFY_CONTAINER_NAME=s888kgk480ww8s0w88o0gwso-021904276222' --progress plain -t s888kgk480ww8s0w88o0gwso:98de18cabf09af55bfa61ea711c866ec36fe71d1 /artifacts/nco4cg0oo8go8og80c0wkg4o
2025-Jul-27 02:19:23.562688
[CMD]: docker exec nco4cg0oo8go8og80c0wkg4o bash -c 'bash /artifacts/build.sh'
2025-Jul-27 02:19:23.562688
#0 building with "default" instance using docker driver
2025-Jul-27 02:19:23.562688
2025-Jul-27 02:19:23.562688
#1 [internal] load build definition from Dockerfile
2025-Jul-27 02:19:23.562688
#1 transferring dockerfile: 1.11kB done
2025-Jul-27 02:19:23.562688
#1 DONE 0.0s
2025-Jul-27 02:19:23.562688
2025-Jul-27 02:19:23.562688
#2 [internal] load metadata for ghcr.io/railwayapp/nixpacks:ubuntu-**********
2025-Jul-27 02:19:24.457445
#2 DONE 1.0s
2025-Jul-27 02:19:24.602105
#3 [internal] load .dockerignore
2025-Jul-27 02:19:24.602105
#3 transferring context: 2B done
2025-Jul-27 02:19:24.602105
#3 DONE 0.0s
2025-Jul-27 02:19:24.602105
2025-Jul-27 02:19:24.602105
#4 [ 1/13] FROM ghcr.io/railwayapp/nixpacks:ubuntu-**********@sha256:ed406b77fb751927991b8655e76c33a4521c4957c2afeab293be7c63c2a373d2
2025-Jul-27 02:19:24.602105
#4 resolve ghcr.io/railwayapp/nixpacks:ubuntu-**********@sha256:ed406b77fb751927991b8655e76c33a4521c4957c2afeab293be7c63c2a373d2 0.0s done
2025-Jul-27 02:19:24.704986
#4 sha256:53a1550a769bb935ad1d80f12dfd0ae96a8ec35865d8e6b956cdb79f3ae49e5e 868B / 868B done
2025-Jul-27 02:19:24.704986
#4 sha256:82afdcbbf1dd44bb728577e4e48f1c9ca00dde78318a60985b01018dc2803a46 4.37kB / 4.37kB done
2025-Jul-27 02:19:24.704986
#4 sha256:9cb31e2e37eab1bff50f727e979fcacb509e225fb853433a6fe21d2fb34e6305 0B / 29.54MB 0.2s
2025-Jul-27 02:19:24.704986
#4 sha256:1c935ba1187dfc842c26f7bce427d4eb5ed21246b95b7294677ea04990aa4fbd 0B / 40.23MB 0.2s
2025-Jul-27 02:19:24.704986
#4 sha256:f4398153a88aa9635ea062867fd6e14e5332ca500d71705109f33bc0859c772c 0B / 34.78MB 0.2s
2025-Jul-27 02:19:24.704986
#4 sha256:ed406b77fb751927991b8655e76c33a4521c4957c2afeab293be7c63c2a373d2 1.61kB / 1.61kB done
2025-Jul-27 02:19:24.911774
#4 sha256:9cb31e2e37eab1bff50f727e979fcacb509e225fb853433a6fe21d2fb34e6305 7.34MB / 29.54MB 0.4s
2025-Jul-27 02:19:24.911774
#4 sha256:1c935ba1187dfc842c26f7bce427d4eb5ed21246b95b7294677ea04990aa4fbd 7.34MB / 40.23MB 0.4s
2025-Jul-27 02:19:25.124444
#4 sha256:9cb31e2e37eab1bff50f727e979fcacb509e225fb853433a6fe21d2fb34e6305 19.92MB / 29.54MB 0.6s
2025-Jul-27 02:19:25.124444
#4 sha256:1c935ba1187dfc842c26f7bce427d4eb5ed21246b95b7294677ea04990aa4fbd 16.78MB / 40.23MB 0.6s
2025-Jul-27 02:19:25.349394
#4 sha256:9cb31e2e37eab1bff50f727e979fcacb509e225fb853433a6fe21d2fb34e6305 24.34MB / 29.54MB 0.7s
2025-Jul-27 02:19:25.349394
#4 sha256:1c935ba1187dfc842c26f7bce427d4eb5ed21246b95b7294677ea04990aa4fbd 19.92MB / 40.23MB 0.7s
2025-Jul-27 02:19:25.349394
#4 sha256:f4398153a88aa9635ea062867fd6e14e5332ca500d71705109f33bc0859c772c 2.10MB / 34.78MB 0.7s
2025-Jul-27 02:19:25.473478
#4 sha256:9cb31e2e37eab1bff50f727e979fcacb509e225fb853433a6fe21d2fb34e6305 28.31MB / 29.54MB 0.8s
2025-Jul-27 02:19:25.473478
#4 sha256:1c935ba1187dfc842c26f7bce427d4eb5ed21246b95b7294677ea04990aa4fbd 25.50MB / 40.23MB 0.9s
2025-Jul-27 02:19:25.473478
#4 sha256:f4398153a88aa9635ea062867fd6e14e5332ca500d71705109f33bc0859c772c 7.39MB / 34.78MB 0.9s
2025-Jul-27 02:19:25.611423
#4 sha256:9cb31e2e37eab1bff50f727e979fcacb509e225fb853433a6fe21d2fb34e6305 29.54MB / 29.54MB 0.9s done
2025-Jul-27 02:19:25.611423
#4 sha256:1c935ba1187dfc842c26f7bce427d4eb5ed21246b95b7294677ea04990aa4fbd 33.55MB / 40.23MB 1.1s
2025-Jul-27 02:19:25.611423
#4 sha256:f4398153a88aa9635ea062867fd6e14e5332ca500d71705109f33bc0859c772c 14.68MB / 34.78MB 1.1s
2025-Jul-27 02:19:25.747990
#4 sha256:1c935ba1187dfc842c26f7bce427d4eb5ed21246b95b7294677ea04990aa4fbd 39.85MB / 40.23MB 1.2s
2025-Jul-27 02:19:25.747990
#4 sha256:f4398153a88aa9635ea062867fd6e14e5332ca500d71705109f33bc0859c772c 23.07MB / 34.78MB 1.2s
2025-Jul-27 02:19:25.862661
#4 sha256:1c935ba1187dfc842c26f7bce427d4eb5ed21246b95b7294677ea04990aa4fbd 40.23MB / 40.23MB 1.2s done
2025-Jul-27 02:19:25.862661
#4 sha256:f4398153a88aa9635ea062867fd6e14e5332ca500d71705109f33bc0859c772c 32.51MB / 34.78MB 1.3s
2025-Jul-27 02:19:25.862661
#4 extracting sha256:9cb31e2e37eab1bff50f727e979fcacb509e225fb853433a6fe21d2fb34e6305
2025-Jul-27 02:19:25.983104
#4 sha256:f4398153a88aa9635ea062867fd6e14e5332ca500d71705109f33bc0859c772c 34.78MB / 34.78MB 1.4s done
2025-Jul-27 02:19:30.977802
#4 extracting sha256:9cb31e2e37eab1bff50f727e979fcacb509e225fb853433a6fe21d2fb34e6305 5.1s
2025-Jul-27 02:19:34.132076
#4 ...
2025-Jul-27 02:19:34.132076
2025-Jul-27 02:19:34.132076
#5 [internal] load build context
2025-Jul-27 02:19:34.132076
#5 transferring context: 95.44MB 9.6s
2025-Jul-27 02:19:36.077183
#5 transferring context: 142.73MB 11.5s done
2025-Jul-27 02:19:36.404321
#5 DONE 11.7s
2025-Jul-27 02:19:36.404321
2025-Jul-27 02:19:36.404321
#4 [ 1/13] FROM ghcr.io/railwayapp/nixpacks:ubuntu-**********@sha256:ed406b77fb751927991b8655e76c33a4521c4957c2afeab293be7c63c2a373d2
2025-Jul-27 02:19:36.404321
#4 extracting sha256:9cb31e2e37eab1bff50f727e979fcacb509e225fb853433a6fe21d2fb34e6305 10.3s
2025-Jul-27 02:19:37.006647
#4 extracting sha256:9cb31e2e37eab1bff50f727e979fcacb509e225fb853433a6fe21d2fb34e6305 11.1s done
2025-Jul-27 02:19:37.006647
#4 extracting sha256:1c935ba1187dfc842c26f7bce427d4eb5ed21246b95b7294677ea04990aa4fbd
2025-Jul-27 02:19:39.602518
#4 extracting sha256:1c935ba1187dfc842c26f7bce427d4eb5ed21246b95b7294677ea04990aa4fbd 2.5s done
2025-Jul-27 02:19:39.602518
#4 extracting sha256:f4398153a88aa9635ea062867fd6e14e5332ca500d71705109f33bc0859c772c
2025-Jul-27 02:19:41.140123
#4 extracting sha256:f4398153a88aa9635ea062867fd6e14e5332ca500d71705109f33bc0859c772c 1.4s done
2025-Jul-27 02:19:41.140123
#4 DONE 16.7s
2025-Jul-27 02:19:41.291283
2025-Jul-27 02:19:41.306167
#6 [ 2/13] WORKDIR /app/
2025-Jul-27 02:19:41.331825
#6 DONE 0.2s
2025-Jul-27 02:19:41.518556
#7 [ 3/13] COPY .nixpacks/nixpkgs-e24b4c09e963677b1beea49d411cd315a024ad3a.nix .nixpacks/nixpkgs-e24b4c09e963677b1beea49d411cd315a024ad3a.nix
2025-Jul-27 02:19:41.518556
#7 DONE 0.0s
2025-Jul-27 02:19:41.518556
2025-Jul-27 02:19:41.518556
#8 [ 4/13] RUN nix-env -if .nixpacks/nixpkgs-e24b4c09e963677b1beea49d411cd315a024ad3a.nix && nix-collect-garbage -d
2025-Jul-27 02:19:41.518556
#8 0.149 unpacking 'https://github.com/NixOS/nixpkgs/archive/e24b4c09e963677b1beea49d411cd315a024ad3a.tar.gz' into the Git cache...
2025-Jul-27 02:20:38.672403
#8 57.30 unpacking 'https://github.com/railwayapp/nix-npm-overlay/archive/main.tar.gz' into the Git cache...
2025-Jul-27 02:20:39.590235
#8 58.22 installing 'e24b4c09e963677b1beea49d411cd315a024ad3a-env'
2025-Jul-27 02:20:42.659232
#8 61.29 these 8 derivations will be built:
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/7r2gqdwc4m6yykzghiz9j3d0jdwg5cjv-builder.pl.drv
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/h6lfwbf28y4hz6rh078jpmwawcgd5nv0-npm-9.9.4.tgz.drv
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/a32hqx0622bhnzcjxh3csqswnsycgm6m-npm.drv
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/cnhv5ydyaxsw8dhvydc5jm4pkrzrgjq4-libraries.drv
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/gx837sa0kcz1lzycss0sa8alab34ib7h-e24b4c09e963677b1beea49d411cd315a024ad3a-env.drv
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/j41ay1gsqacza8zmgssm56ny9szsc1r6-php-extra-init-8.2.27.ini.drv
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/x5rljg8j5a2x0vdiklz6dz3qc4k49zaj-php-with-extensions-8.2.27.drv
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/20wn0b6rjdaxgambrw1d82xls6prv1g5-e24b4c09e963677b1beea49d411cd315a024ad3a-env.drv
2025-Jul-27 02:20:42.659232
#8 61.29 these 248 paths will be fetched (184.39 MiB download, 883.98 MiB unpacked):
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/31pf7m3fxyj7mlz9yr4n9l44lmx716ii-7zz-24.09
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/gmfwlnb6rwda4bwzih1cm4py494ld11r-acl-2.3.2
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/vls1agkfyb7aa7mpx5p4935z3f0af8bz-attr-2.5.2
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/c03w91ah34z68lwcjxhihyigrnmwk4kk-audit-4.0
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/gwgqdl0242ymlikq9s9s62gkp5cvyal3-bash-5.2p37
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/x9d49vaqlrkw97p9ichdwrnbh013kq7z-bash-interactive-5.2p37
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/vk4mlknqk9yjbqa68a7rvpfxfdw3rad7-binutils-2.43.1
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/nmdfn7hkk0vvhqgacvsc8c8ls0qcrv64-binutils-2.43.1-lib
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/qlzvmgr8w9prdlyys7irqf86p7bndf5b-binutils-wrapper-2.43.1
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/zfxr3cd6j3mgr7rpy0kw587qfnss3g48-bison-3.8.2
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/dvk37b5ajr0zfs74b4npa6zy5lp580sd-brotli-1.1.0-lib
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/cp24rhch20cbns9c9jsw7b6h78rlci3z-bzip2-1.0.8
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/1yyryxp7mh7zsciapi8f1n0mnxkigmf8-bzip2-1.0.8-bin
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/rbbaws9k85zah0d77ggsf3hnsd8cavdd-composer-2.8.4
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/6wgd8c9vq93mqxzc7jhkl86mv6qbc360-coreutils-9.5
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/qcf6xyjwapf5bp1p1dh75l6grfb7gl8v-cracklib-2.10.0
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/qkxw8vcbw4kysfjx1cpjl76x5pf005n1-cryptsetup-2.7.5
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/3m255gw7nh8hm91flbsp23v5fkpv1x8k-curl-8.11.1
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/72hw5mjb97kyc3y1jfla1q65i08aqpi0-curl-8.11.1
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/2vfkdsxi4f7sgbzbmvhw5yhv9fgj7hqr-curl-8.11.1-bin
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/3nf9cc1p9d0jw97sf9racacnz5vyq2d0-curl-8.11.1-bin
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/s569mjxw77rh0lwll868apq0xw5zlpyy-curl-8.11.1-dev
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/hjirnxx1nm49n3prqjmm3x1lax55hky4-curl-8.11.1-man
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/k23x1dins12p1iwazf709qm76c8rdw8r-cyrus-sasl-2.1.28
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/fyg90h278plqsmwcs557zrjrqb2krz8v-dav1d-1.5.0
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/4v6sa1avslbqhyphl67jmqgjwkky91pb-dav1d-1.5.0-dev
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/fk2psrg5k3i6z7az5p2bkjc5721hcglw-db-4.8.30
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/9ga2fkz4whkrnw0l7afpb1rsmbjxsk41-db-5.3.28
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/zlwgbgv327a5vhb9ycacixil4q97iibi-dejavu-fonts-minimal-2.37
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/mxi53f9xhkshf3la64awqacfmx9ppkzw-die-hook
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/3sln66ij8pg114apkd8p6nr04y37q5z2-diffutils-3.10
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/kbcplg8kjhh31qy859gr49r0s9n92xqf-dns-root-data-2024-06-20
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/b07fkj0ib4wcv6n0bcxgzmqxkwjq6fv1-ed-1.20.2
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/m9lims7vfas44fh5kg2dc8lz09wwbk9i-elfutils-0.191
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/4xpviqy8kghz7410p5aagdhykcjcpnd1-expand-response-params
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/dl2q06nxjxz7b2dmpkrvjzdyk0plpxhs-expat-2.6.4
2025-Jul-27 02:20:42.659232
#8 61.29   /nix/store/9wbpsj6ksd16x1qdqs29xli1dpz3fnl0-file-5.45
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/hcw121xziyns0lp35vfgqhygy80hdl6c-file-5.45
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/r99d2m4swgmrv9jvm4l9di40hvanq1aq-findutils-4.10.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/mm4i39vjjx6qvl0g0jdyw7pvwpgv8zcc-flex-2.6.4
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/82s97b2cflzqvcg96glqpyjqw19dqz1h-fontconfig-2.15.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/ky4chw5yg9n9rrljxaiahqmmvzrpds31-fontconfig-2.15.0-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/5fhdd79mvzlhvkil71fz5l14ny22w2g2-freetype-2.13.3
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/scgfwh3z1s3l2vhvyjsgfgx5ql552sls-gawk-5.3.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/62qjb50708fdhb4f2y7zxyqr1afir4fk-gcc-13.3.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/4gk773fqcsv4fh2rfkhs9bgfih86fdq8-gcc-13.3.0-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/bmjqxvy53752b3xfvbab6s87xq06hxbs-gcc-13.3.0-libgcc
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/888bkaqdpfpx72dd8bdc69qsqlgbhcvf-gcc-wrapper-13.3.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/5g9b8p16cbyhg36qiajijp6gkv944hp2-gd-2.3.3
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/qyw4nyrsvr43cd6p6nky2134raf3r02s-gdk-pixbuf-2.42.12
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/61b9vs5swzi6nsvydcax0w5wi4yzmlzb-getent-glibc-2.40-36
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/7pls6w3nqphhg33sfwr8yipdf4wlr1mp-gettext-0.21.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/k7bgajd0msc4jh5amlarl4s43yd9g8af-giflib-5.2.2
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/bkrsswbcbj0isdk1nwdqj1sn9jijlms5-git-minimal-2.47.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/xdxwr90hkhmy6kqnbkmz52p5y9ijhbbb-glib-2.82.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/nqb2ns2d1lahnd5ncwmn6k84qfd7vx2k-glibc-2.40-36
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/29mb4q8b5306f4gk2wh38h0c1akb0n97-glibc-2.40-36-bin
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/lcxvgkg659vbvdq86mhxa599wn48f35c-glibc-2.40-36-dev
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/yy9qdbjf9dsmw9b51wpcya293sr2blcf-glibc-2.40-36-getent
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/kwq87k2nykp8fkw8jgqav4jl5nw65isv-gmp-6.3.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/cpyk8ksm7c79l0nabd576s80wngdzix5-gmp-with-cxx-6.3.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/jav2ywbxm3ibmhfslc81sypqhssi3wvb-gmp-with-cxx-6.3.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/0yl1wf4jim6830k2m3c3v5kyp2l9z8ay-gnu-config-2024-01-01
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/vniy1y5n8g28c55y7788npwc4h09fh7c-gnugrep-3.11
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/j4yj2326w7y223p82van51lkli7h6h81-gnum4-1.4.19
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/hbzw8k8ygv6bfzvsvnd4gb8qmm8xjbvn-gnumake-4.4.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/ypwpd5qznmj896akf9hpryyiqig3drir-gnupg-2.4.5
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/yq39xdwm4z0fhx7dsm8mlpgvcz3vbfg3-gnused-4.9
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/0wqn2k3v5xzrc9rwinijdyr2ywwl82x4-gnutar-1.35
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/1sjds2dmnrcfh6ni93v272y27mkf6aa2-gnutls-3.8.6
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/5y240z436gf3rwmkwbhn1a17pqw509w4-gzip-1.13
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/q5wfidiavi63f09b2lc54mnrgywplxyi-icu4c-73.2
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/aiw8lrc9f115z9fj6khijgk9frj7i772-icu4c-74.2
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/26b79gadv22qirdkl82pnx58z19x8p1r-icu4c-74.2-dev
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/hk52s3rw8xf0dbrlbbvjgc4qp9hdmr3i-iptables-1.8.10
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/idpsjqf760ap2vr49hkdshhj8sfdc6mn-isl-0.20
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/mqhqmkkc7l2krbgw4z9vgyj37brq6jzs-json-c-0.17
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/a0fbsn0xx6c1zviv2y24s2ryy2jdj6k0-kbd-2.6.4
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/j3x77lgarc734is29divi4hj1grfv69c-kexec-tools-2.0.29
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/5mq02cggv5riwg4n7w4amddjgv6b5fnj-keyutils-1.6.3-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/h16ifxs07jpm4gj6a4gwi1zx66yvl2mw-kmod-31
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/y8k4wbgnkvdr7k10irr73s6ynjd9jg3p-kmod-31-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/dak3fx41z361mc0axfwrzian4dg1djn6-krb5-1.21.3
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/gwp6yi4ywldkpd5cs4yky9kj7cc1dcrg-krb5-1.21.3-dev
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/9hhs5ffbb2cl03bvzifi2a76xg2rf2wk-krb5-1.21.3-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/b4hmjzgk6w13525nwgb04xpmd71g4nbx-krb5-1.21.3-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/17y91dqjg4wkafdz9swgm43az3sz42fh-lerc-4.0.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/zcq4irdcgn3ljqdnlpm2zjp7f1kw9jvm-libX11-1.8.10
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/6a49zj2wva8nxw7sidw9j9bp2nifscbw-libXau-1.0.11
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/1x0fg2bhf14lk83458c4iir92hc1njjh-libXdmcp-1.1.5
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/ybac75n8pll3xq09iz4jyccn7i8paiwb-libXpm-3.5.17
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/y8vghanhgfjdzivp1p6d2fff5j8iw112-libaom-3.10.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/sy1n9pdjhvvk5lkx07jz1ynacpw75w5v-libaom-3.10.0-bin
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/83kw97s7widwn3rivl6gii26njlb5crs-libaom-3.10.0-dev
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/185sr2mnma0nszglad6yjmqip0lzf3qf-libapparmor-4.0.3
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/pqjhj3mbzswhajlbf3nb5m6z06b6bqv6-libarchive-3.7.7-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/rgya7avxpibqffw3ybfdm2hcx2rm6fdy-libargon2-20190702
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/z1si2srd020024zc72xvnkpbxfi23n25-libassuan-2.5.7
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/a0bbn7qyxgyh4jhf9dgwq3l6j12qpn4f-libavif-1.1.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/fwgfjb113gr2a5cw1lnkc4f48ccjrzd7-libbpf-1.4.7
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/ay424b8c8zv4jviiml73b3qdgv8r83r7-libcap-2.70-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/cbad0ssjzbgxmv64pgk63p9mhxrsaqlk-libcbor-0.11.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/1xc81bcdi2y78v8j6l28mmgy8la7gxnr-libdeflate-1.22
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/cqz2b6rs1lcvp00545fff5x7ykgvyl7d-libevent-2.1.12
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/6pak77li0iw9x0b3yhmbjvp846w3p6bx-libffi-3.4.6
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/7f8k48xpd2mzaz166azfn8sz5dqi2z33-libfido2-1.15.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/2vvar8jpga3r97pcvzwrckvibsv5gf3f-libgcrypt-1.10.3-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/9570v4xfk5hzy3inlwpw785p24zwgfl7-libgpg-error-1.50
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/md2ngw10z7ydav3f35qp7ypcnm4dnxzq-libjpeg-turbo-3.0.4
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/n753kvn23nh449ix430dxgwz2aznwbwl-libmicrohttpd-1.0.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/gw6pzr3wh5n165qy74332dgs0y1v7ds0-libmnl-1.0.5
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/w75dbhs3yxq98309az0ybgrg2afq8g1p-libmpc-1.3.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/39hrbcf2kpdxxp7xcg906j7a87176885-libnetfilter_conntrack-1.1.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/9b41wrq1jd5yvnip9004x7dk58xyg536-libnfnetlink-1.0.2
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/fr5ald09w424livkvl4kagkn5ybk7dzs-libnftnl-1.2.8
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/rlvxljwqk6348h7znms45jd1n6krlslg-libnl-3.10.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/wra4h1zk0k4kaidsj0bx662wccyb6fkd-libpcap-1.10.5
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/1mldak8ls62za08yckviy3zfpzim4aiz-libpng-apng-1.6.43
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/lai3ymn295qmvqdbgiqs06xl9l9j21w7-libpsl-0.21.5
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/qxwppk9r13zqn5rp13h7f1zc8b4xqfbw-libpwquality-1.4.5-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/0yq7vgb3nkkj0x07vzj180gj2yljlpcb-libseccomp-2.5.5-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/mildf9x8k10pqzgfm54xhnhwrlmcl953-libselinux-3.7
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/g81bf2rcsh5r24dimbfnkikhfslzy1a9-libsodium-1.0.20
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/6sra1zhqva6z29dmp4xakylhdjrpf72k-libssh2-1.11.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/lsvh9q7b0qcjwxd7m2ihby4r3hn0dw9i-libssh2-1.11.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/pbwwimrnl5wzp17fyvxls48wx71i2agk-libssh2-1.11.1-dev
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/5mjwp2vnwq59i27c44kwb7n5gsqrlizy-libtasn1-4.19.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/nwn9micsii2qax3bfx9yr48276163bqg-libtiff-4.7.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/aydczwv35369q1lgbrjvkgmcrx1kzjmj-libtool-2.4.7-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/45ick6cn78pkrqmm2zr73v1s5vryxfmc-libuv-1.48.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/llwfx8zsw3n474wlkwyhm3wl223d68vm-libuv-1.48.0-dev
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/nv1iyrrq6wk450qp41dgqqdvc4m51zdw-libvmaf-3.0.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/dvxbm47x918j56xz9bhqlj9pckbfm8if-libvmaf-3.0.0-dev
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/0hp36aakc6gswshgdzpvfvgb090fpj58-libwebp-1.4.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/6y1gr6bh62cxfl6wh46hqjjmys5s2mry-libwebp-1.4.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/c25p9xs9n6grwx4i4l4kmz09scgcav4b-libxcb-1.17.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/a7x8s7d3bhn41dqrai6nf5xszizb7fm2-libxcrypt-4.4.36
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/0z4hrksbdrwv9xb8ycjk3rq9ppmw0350-libxml2-2.13.5
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/b7svpr3lnh4m8zlchsjz3hs9i3ri1q3s-libxslt-1.1.42
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/7918mvq3835h7aa8pa4icdq2q5c83p3d-libyuv-1787
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/0kkjw672jszig156v0a83a8cg0zxin8d-libzip-1.11.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/dvsai0ym9czjl5mcsarcdwccb70615n4-linux-headers-6.10
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/sl3fa5zh61xxl03m64if2wqzbvrb6zly-linux-pam-1.6.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/sbr9bjn36i9ibh5hmdni1p3nrlbkm7qc-lndir-1.0.5
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/zkcf7gg7kdd7r1vxjmcb8ykcgs70wrqy-lvm2-2.03.27-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/kpfi383adzn90gnzjqpva2layjb9cylk-lz4-1.10.0-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/vi2j2pqg0vcwrq9nv1lcxqr9czfbfza2-make-binary-wrapper-hook
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/mijvnwpkic0w59adx8gpds5wxi3plf07-mariadb-connector-c-3.3.5
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/lfwvcxbhmqnpmqcxbpd5bwx3bhkpjgci-mirrors-list
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/simak2bypwin0skwr4p9pbgccnyi1jph-mpfr-4.2.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/0fvqpcv5ds4q44xbhvh3gfd1gmngmb28-ncompress-5.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/k3a7dzrqphj9ksbb43i24vy6inz8ys51-ncurses-6.4.20221231
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/nxkf552xmhw7gpff39l541lzbql46x7k-nettle-3.10
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/gqa5wiphbpgxnqhn33mkj0j7wvj3651s-nghttp2-1.64.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/v1h6rvf3ly2s0zwxsck34bfy7gbrfbcj-nghttp2-1.64.0-dev
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/5b4s9pww5j7s6wnh6l953pizyp9za2wl-nghttp2-1.64.0-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/r3b4raa1z7pj73j4sri2fpwp0rsn6zhb-nghttp2-1.64.0-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/8lncw1wjkrywc0shnwhpsgwgj9cn4da1-nginx-1.26.2
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/lib106in05zrixvpgm5jxg8jsasrcpcc-nodejs-18.20.5
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/zigiq005qdw4hm9kpb3z5kjvg3fipndf-npth-1.7
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/8az76ng86zh4m4383qwf57kv9w0140vc-oniguruma-6.9.9-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/mkhangs7d5gdl5r0mf7irylf7sldjkfx-openldap-2.6.9
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/q0plci6b8x9m08k61jg89dc77hlhmghz-openssl-3.3.2
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/zs44kdd3k01schy32fa916pa17gr7y68-openssl-3.3.2
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/490zkpvg0q41z3937i1ifwp2ygwgkihf-openssl-3.3.2-bin
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/rc5j45brxiyl55fgd3adbcc43pdfr29v-openssl-3.3.2-bin
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/74z4cw9g5fhzkhydpsaac9d41b073dy6-openssl-3.3.2-dev
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/sc25r6y9q3b1ccjjj3fl79rdj6sjs91l-openssl-3.3.2-dev
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/j9wyqisff3vx60n87kmzv8lmrrzr917a-p11-kit-0.25.5
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/rfrjws98w6scfx7m63grb0m6sg925ahd-patch-2.7.6
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/wnl9qpnhayry14lhcbdafhadsjwsdr6p-patchelf-0.15.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/1i1sq7gzlzw7q2q8a2f2hmvsa8az258d-pcre2-10.44
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/s3nhsilxmybnl8nn6vdnf1qi3p81415p-pcre2-10.44
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/anzlacc7ig1cq035xmwpliqnsjr5yd07-pcsclite-2.3.0-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/ygz7vmxcmm3p9md71ck883qdlkfi7c62-perl-5.40.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/nv7agllrn6xvfyxqjg55l2nj3n18sgqc-php-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/2w9j6vi0qhdyxjdkyf36lah23zpl3051-php-bcmath-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/dqf07dnn2li4z721flnsmmrnsk7n0s1h-php-calendar-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/mlvvxh0bgnj6p7rnpm11k3n7bl0fm9hs-php-ctype-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/d21n7nqxhkpd4cskh8i612mrv96kyak9-php-curl-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/ayicm0pi1iqs07inw9pblmfzhjhfbd2i-php-dom-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/hlyv3ypzy1asia2gj8f4qy6dnnprvdxm-php-exif-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/iqip6spnxj3696lmmr9gdxrk3z54n7dw-php-extra-init-8.2.27.ini
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/fp194a1rk74cik25dyylbgyrryll87s8-php-fileinfo-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/9g546ak9kcmm2a18vjaclb71p31zllmg-php-filter-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/fmhg1xw1nk77khakc359hyf3vnhnkr67-php-ftp-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/13ar1lxax1zrnl2fwim2gc06a1fij2a8-php-gd-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/k1z2n1m93ni684a2j6s5ixxy5fcaq897-php-gettext-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/cwd5g91gxm1jzi2a35xq96rz65kn2vpy-php-gmp-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/sz2pyj2wyb4swj2bbslj5nnhilq527wq-php-iconv-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/vgr999zbckcbkvaybpbqn510xgjslcfl-php-imap-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/fl07kp4ckh8fnylwv7z8yy7gym4mbab3-php-intl-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/1ryb89bvwapvi114wcgpcx5s7kibs8f8-php-ldap-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/79xws3iflkk0cvnm3yam7jbw9mmj6qkr-php-mbstring-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/zzdk2b7kh3k02mw1j0snlzrnswp6w2n9-php-mysqli-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/smdcnyx785fmr6z45j34xkba04wyq3ni-php-mysqlnd-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/zj253anmmfqr6l5cp1l53qflkxr4cvv5-php-opcache-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/2h30fwd8w2s7xgf96gv334s9bbf78qyb-php-openssl-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/ih86hc1x4lx7dqrr3zppavw30s44bmf0-php-openssl-8.2.27-dev
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/mirrv7j19fb4g3xpl3g2d3z68ra4x3dd-php-pcntl-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/yq84qz7ak3xklafn4inawqz1vw3i4hn1-php-pdo-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/8ldz8y1fl60g7yv9a09b8zlgnskakv0w-php-pdo_mysql-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/7ghsk2hik136m7m1rf8a8y7bd4qhycci-php-pdo_odbc-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/2kfisxs701km8wfm0lmngsv66gg31b2v-php-pdo_pgsql-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/n9xnph4ijzwr9pz4rk44gb362yh75mqh-php-pdo_sqlite-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/h2lljkrv615081rk2d69j9mdr7sis7sj-php-pgsql-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/l23hqzy592p5c0cnnbw3gj9izkmn1wps-php-posix-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/zjgxcv2c3my7y99ykjxh98hnvnypbfsk-php-readline-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/pz8n2xm9flh1g29dm7ksa54c45zydc62-php-session-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/jb170sz561ckx20k1yc87884yhxnwd7b-php-simplexml-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/vd7cv8925ix1k0807i3fymwxb0w6zqm0-php-soap-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/9bh2yw2fkyl5slfykyxd1cmg49rzwjq5-php-sockets-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/y1wxh6pds9xxnxvcgfzz8iz0zrss8g0x-php-sodium-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/bfz2icynplvd84sjg7f9c8vjyv0brixk-php-sqlite3-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/gfyyvw8ml3aw4pmar15j5vcld9w6ylbh-php-sysvsem-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/qyql0n9sh44n69c2398k0w66a9pwzl0l-php-tokenizer-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/a0qcy2m5mqwrlkr3xkza0n2wdmbq8myq-php-with-extensions-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/cfhkzi86xpdq2grqvc2d3gspk9hp08kq-php-xml-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/637vq1nfs9s01qaa70av48cnc47vjk79-php-xmlreader-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/5jmqlg9z2shxrhadmbqppvignw23k0s7-php-xmlwriter-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/7z4p6h0y0n2ii1bn0ky3dgyvmhj3c3v2-php-zip-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/pz3d1dyrf30iz5q3lnqqr7fpbxzsc53b-php-zlib-8.2.27
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/z423par8g8w54awcns67lb0b6vdz1z7i-postgresql-16.6-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/8lwbq18vjygxgr7d8nrhsjsk1zq257gs-publicsuffix-list-0-unstable-2024-10-25
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/z815hr40d2sc1ngn2jfgsr257g7gxjk6-qrencode-4.1.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/8yff66km6d5mav6i2101kmvp78vgqfcc-readline-8.2p13
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/dsqzw96w4sxsp4q9yvkfl2yh701mpwgi-sqlite-3.46.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/cf464y41p2x3lh1qvbg6678lc3f8zbd6-stdenv-linux
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/lzrs17sc8bhi87nb1y1q1bas73j6q10y-stdenv-linux
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/8dzr2bndjm1rblw86fyha9wb1l2876cw-system-sendmail-1.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/bl5dgjbbr9y4wpdw6k959mkq4ig0jwyg-systemd-256.10
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/qzpdmv5hnf52ln42hkbc874q37rrmbyy-systemd-minimal-libs-256.10
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/mm0hlk3mfzddlcgk4235lxbi2fp1gn77-tpm2-tss-4.1.3
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/2rbjg2k2rv73vr8jm5avwrb5d77wzf7l-unbound-1.21.1-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/w02flr1vr42wcyvpw5nxbx3illkfas13-unixODBC-2.3.12
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/grpfa6f4czjg856b75xcsfan5qznxh6s-unzip-6.0
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/ap724yhgv28mpsi1mmqcwypj4rrfhqmg-update-autotools-gnu-config-scripts-hook
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/qmn58s05ay763vzi7vxax0a6kw37gyjq-util-linux-minimal-2.39.4-lib
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/smivcbakfgwin0d4518byn32rr6h31zg-util-linux-minimal-2.39.4-login
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/id0ns97w93xhwqi8rlwdnvy6zyhv04hn-util-linux-minimal-2.39.4-mount
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/fsh0l8jq8sjyh2a3kxd5v9wlpss745hb-util-linux-minimal-2.39.4-swap
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/bmzls7bipx954xsnbgshh8kbi488zy5m-xz-5.6.3
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/4i4mjaf7z6gddspar487grxk5k1j4dcd-xz-5.6.3-bin
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/l5g2v1jgfyf3j0jp9iv5b79fi8yrwzpp-zlib-1.3.1
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/06q0p7bhn2ffxxya20rrfqdib3h32csn-zlib-1.3.1-dev
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/fxkmblfp6k5fskjaxzkkhixh1xlrd6zj-zlib-ng-2.2.2
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/afdaxacrxy5jgh5pmm3a2xn3hny6wdq7-zstd-1.5.6
2025-Jul-27 02:20:42.668664
#8 61.29   /nix/store/qpmg2piws75wmy1619ryh0dhwi9638ni-zstd-1.5.6-bin
2025-Jul-27 02:20:42.801673
#8 61.33 copying path '/nix/store/mxi53f9xhkshf3la64awqacfmx9ppkzw-die-hook' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:42.801673
#8 61.34 copying path '/nix/store/bmjqxvy53752b3xfvbab6s87xq06hxbs-gcc-13.3.0-libgcc' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:42.812747
#8 61.34 copying path '/nix/store/0yl1wf4jim6830k2m3c3v5kyp2l9z8ay-gnu-config-2024-01-01' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:42.812747
#8 61.35 copying path '/nix/store/zlwgbgv327a5vhb9ycacixil4q97iibi-dejavu-fonts-minimal-2.37' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:42.812747
#8 61.35 copying path '/nix/store/nqb2ns2d1lahnd5ncwmn6k84qfd7vx2k-glibc-2.40-36' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:42.812747
#8 61.35 copying path '/nix/store/8lwbq18vjygxgr7d8nrhsjsk1zq257gs-publicsuffix-list-0-unstable-2024-10-25' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:42.812747
#8 61.36 copying path '/nix/store/dvsai0ym9czjl5mcsarcdwccb70615n4-linux-headers-6.10' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:42.812747
#8 61.36 copying path '/nix/store/kbcplg8kjhh31qy859gr49r0s9n92xqf-dns-root-data-2024-06-20' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:42.812747
#8 61.37 copying path '/nix/store/lfwvcxbhmqnpmqcxbpd5bwx3bhkpjgci-mirrors-list' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:42.812747
#8 61.37 copying path '/nix/store/hjirnxx1nm49n3prqjmm3x1lax55hky4-curl-8.11.1-man' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:42.812747
#8 61.37 copying path '/nix/store/gqa5wiphbpgxnqhn33mkj0j7wvj3651s-nghttp2-1.64.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:42.812747
#8 61.38 copying path '/nix/store/ap724yhgv28mpsi1mmqcwypj4rrfhqmg-update-autotools-gnu-config-scripts-hook' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:42.812747
#8 61.43 copying path '/nix/store/82s97b2cflzqvcg96glqpyjqw19dqz1h-fontconfig-2.15.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.629817
#8 62.26 copying path '/nix/store/vls1agkfyb7aa7mpx5p4935z3f0af8bz-attr-2.5.2' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.644027
2025-Jul-27 02:20:43.739697
#8 62.26 copying path '/nix/store/gwgqdl0242ymlikq9s9s62gkp5cvyal3-bash-5.2p37' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.739697
#8 62.26 copying path '/nix/store/dvk37b5ajr0zfs74b4npa6zy5lp580sd-brotli-1.1.0-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.739697
#8 62.26 copying path '/nix/store/cp24rhch20cbns9c9jsw7b6h78rlci3z-bzip2-1.0.8' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.739697
#8 62.27 copying path '/nix/store/fyg90h278plqsmwcs557zrjrqb2krz8v-dav1d-1.5.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.739697
#8 62.27 copying path '/nix/store/b07fkj0ib4wcv6n0bcxgzmqxkwjq6fv1-ed-1.20.2' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.739697
#8 62.27 copying path '/nix/store/4xpviqy8kghz7410p5aagdhykcjcpnd1-expand-response-params' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.739697
#8 62.27 copying path '/nix/store/dl2q06nxjxz7b2dmpkrvjzdyk0plpxhs-expat-2.6.4' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.739697
#8 62.27 copying path '/nix/store/scgfwh3z1s3l2vhvyjsgfgx5ql552sls-gawk-5.3.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.752990
#8 62.27 copying path '/nix/store/4gk773fqcsv4fh2rfkhs9bgfih86fdq8-gcc-13.3.0-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.752990
#8 62.27 copying path '/nix/store/k7bgajd0msc4jh5amlarl4s43yd9g8af-giflib-5.2.2' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.752990
#8 62.27 copying path '/nix/store/29mb4q8b5306f4gk2wh38h0c1akb0n97-glibc-2.40-36-bin' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.752990
#8 62.27 copying path '/nix/store/yy9qdbjf9dsmw9b51wpcya293sr2blcf-glibc-2.40-36-getent' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.752990
#8 62.27 copying path '/nix/store/kwq87k2nykp8fkw8jgqav4jl5nw65isv-gmp-6.3.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.752990
#8 62.28 copying path '/nix/store/hbzw8k8ygv6bfzvsvnd4gb8qmm8xjbvn-gnumake-4.4.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.752990
#8 62.28 copying path '/nix/store/yq39xdwm4z0fhx7dsm8mlpgvcz3vbfg3-gnused-4.9' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.752990
#8 62.33 copying path '/nix/store/mqhqmkkc7l2krbgw4z9vgyj37brq6jzs-json-c-0.17' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.752990
#8 62.35 copying path '/nix/store/61b9vs5swzi6nsvydcax0w5wi4yzmlzb-getent-glibc-2.40-36' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.752990
#8 62.35 copying path '/nix/store/5mq02cggv5riwg4n7w4amddjgv6b5fnj-keyutils-1.6.3-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.752990
#8 62.37 copying path '/nix/store/gmfwlnb6rwda4bwzih1cm4py494ld11r-acl-2.3.2' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.858146
#8 62.41 copying path '/nix/store/6a49zj2wva8nxw7sidw9j9bp2nifscbw-libXau-1.0.11' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.858146
#8 62.45 copying path '/nix/store/1yyryxp7mh7zsciapi8f1n0mnxkigmf8-bzip2-1.0.8-bin' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.858146
#8 62.45 copying path '/nix/store/1x0fg2bhf14lk83458c4iir92hc1njjh-libXdmcp-1.1.5' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.858146
#8 62.49 copying path '/nix/store/185sr2mnma0nszglad6yjmqip0lzf3qf-libapparmor-4.0.3' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.858146
#8 62.49 copying path '/nix/store/rgya7avxpibqffw3ybfdm2hcx2rm6fdy-libargon2-20190702' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.869022
2025-Jul-27 02:20:43.964424
#8 62.49 copying path '/nix/store/ay424b8c8zv4jviiml73b3qdgv8r83r7-libcap-2.70-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.964424
#8 62.49 copying path '/nix/store/cbad0ssjzbgxmv64pgk63p9mhxrsaqlk-libcbor-0.11.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.964424
#8 62.50 copying path '/nix/store/1xc81bcdi2y78v8j6l28mmgy8la7gxnr-libdeflate-1.22' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.964424
#8 62.50 copying path '/nix/store/cqz2b6rs1lcvp00545fff5x7ykgvyl7d-libevent-2.1.12' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.964424
#8 62.52 copying path '/nix/store/0wqn2k3v5xzrc9rwinijdyr2ywwl82x4-gnutar-1.35' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.964424
#8 62.57 copying path '/nix/store/9570v4xfk5hzy3inlwpw785p24zwgfl7-libgpg-error-1.50' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.964424
#8 62.58 copying path '/nix/store/md2ngw10z7ydav3f35qp7ypcnm4dnxzq-libjpeg-turbo-3.0.4' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.964424
#8 62.59 copying path '/nix/store/6pak77li0iw9x0b3yhmbjvp846w3p6bx-libffi-3.4.6' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.964424
#8 62.59 copying path '/nix/store/gw6pzr3wh5n165qy74332dgs0y1v7ds0-libmnl-1.0.5' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:43.964424
#8 62.60 copying path '/nix/store/9b41wrq1jd5yvnip9004x7dk58xyg536-libnfnetlink-1.0.2' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.082080
#8 62.63 copying path '/nix/store/idpsjqf760ap2vr49hkdshhj8sfdc6mn-isl-0.20' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.082080
#8 62.71 copying path '/nix/store/fr5ald09w424livkvl4kagkn5ybk7dzs-libnftnl-1.2.8' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.181709
#8 62.75 copying path '/nix/store/rlvxljwqk6348h7znms45jd1n6krlslg-libnl-3.10.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.190823
#8 62.75 copying path '/nix/store/lai3ymn295qmvqdbgiqs06xl9l9j21w7-libpsl-0.21.5' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.190823
#8 62.75 copying path '/nix/store/0yq7vgb3nkkj0x07vzj180gj2yljlpcb-libseccomp-2.5.5-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.190823
#8 62.77 copying path '/nix/store/39hrbcf2kpdxxp7xcg906j7a87176885-libnetfilter_conntrack-1.1.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.190823
#8 62.77 copying path '/nix/store/g81bf2rcsh5r24dimbfnkikhfslzy1a9-libsodium-1.0.20' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.190823
#8 62.81 copying path '/nix/store/c03w91ah34z68lwcjxhihyigrnmwk4kk-audit-4.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.306907
#8 62.84 copying path '/nix/store/j4yj2326w7y223p82van51lkli7h6h81-gnum4-1.4.19' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.316265
#8 62.84 copying path '/nix/store/5y240z436gf3rwmkwbhn1a17pqw509w4-gzip-1.13' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.316265
#8 62.84 copying path '/nix/store/5mjwp2vnwq59i27c44kwb7n5gsqrlizy-libtasn1-4.19.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.316265
#8 62.89 copying path '/nix/store/aydczwv35369q1lgbrjvkgmcrx1kzjmj-libtool-2.4.7-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.316265
#8 62.89 copying path '/nix/store/45ick6cn78pkrqmm2zr73v1s5vryxfmc-libuv-1.48.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.316265
#8 62.94 copying path '/nix/store/c25p9xs9n6grwx4i4l4kmz09scgcav4b-libxcb-1.17.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.407374
#8 62.94 copying path '/nix/store/a7x8s7d3bhn41dqrai6nf5xszizb7fm2-libxcrypt-4.4.36' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.417641
#8 62.94 copying path '/nix/store/0z4hrksbdrwv9xb8ycjk3rq9ppmw0350-libxml2-2.13.5' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.417641
#8 62.95 copying path '/nix/store/sbr9bjn36i9ibh5hmdni1p3nrlbkm7qc-lndir-1.0.5' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.417641
#8 62.97 copying path '/nix/store/kpfi383adzn90gnzjqpva2layjb9cylk-lz4-1.10.0-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.417641
#8 62.97 copying path '/nix/store/simak2bypwin0skwr4p9pbgccnyi1jph-mpfr-4.2.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.417641
#8 62.98 copying path '/nix/store/4v6sa1avslbqhyphl67jmqgjwkky91pb-dav1d-1.5.0-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.417641
#8 62.99 copying path '/nix/store/z1si2srd020024zc72xvnkpbxfi23n25-libassuan-2.5.7' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.417641
#8 63.04 copying path '/nix/store/2vvar8jpga3r97pcvzwrckvibsv5gf3f-libgcrypt-1.10.3-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.558977
#8 63.06 copying path '/nix/store/llwfx8zsw3n474wlkwyhm3wl223d68vm-libuv-1.48.0-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.572586
#8 63.06 copying path '/nix/store/0fvqpcv5ds4q44xbhvh3gfd1gmngmb28-ncompress-5.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.572586
#8 63.07 copying path '/nix/store/k3a7dzrqphj9ksbb43i24vy6inz8ys51-ncurses-6.4.20221231' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.572586
#8 63.10 copying path '/nix/store/5b4s9pww5j7s6wnh6l953pizyp9za2wl-nghttp2-1.64.0-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.572586
#8 63.10 copying path '/nix/store/lcxvgkg659vbvdq86mhxa599wn48f35c-glibc-2.40-36-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.572586
#8 63.11 copying path '/nix/store/r3b4raa1z7pj73j4sri2fpwp0rsn6zhb-nghttp2-1.64.0-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.572586
#8 63.13 copying path '/nix/store/zigiq005qdw4hm9kpb3z5kjvg3fipndf-npth-1.7' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.572586
#8 63.14 copying path '/nix/store/8az76ng86zh4m4383qwf57kv9w0140vc-oniguruma-6.9.9-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.572586
#8 63.19 copying path '/nix/store/q0plci6b8x9m08k61jg89dc77hlhmghz-openssl-3.3.2' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.692588
#8 63.21 copying path '/nix/store/zfxr3cd6j3mgr7rpy0kw587qfnss3g48-bison-3.8.2' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.692588
#8 63.21 copying path '/nix/store/mm4i39vjjx6qvl0g0jdyw7pvwpgv8zcc-flex-2.6.4' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.692588
#8 63.21 copying path '/nix/store/wra4h1zk0k4kaidsj0bx662wccyb6fkd-libpcap-1.10.5' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.692588
#8 63.23 copying path '/nix/store/zs44kdd3k01schy32fa916pa17gr7y68-openssl-3.3.2' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.692588
#8 63.23 copying path '/nix/store/j9wyqisff3vx60n87kmzv8lmrrzr917a-p11-kit-0.25.5' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.692588
#8 63.26 copying path '/nix/store/v1h6rvf3ly2s0zwxsck34bfy7gbrfbcj-nghttp2-1.64.0-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.692588
#8 63.32 copying path '/nix/store/w75dbhs3yxq98309az0ybgrg2afq8g1p-libmpc-1.3.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.794878
#8 63.34 copying path '/nix/store/rfrjws98w6scfx7m63grb0m6sg925ahd-patch-2.7.6' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.794878
#8 63.34 copying path '/nix/store/1i1sq7gzlzw7q2q8a2f2hmvsa8az258d-pcre2-10.44' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.794878
#8 63.43 copying path '/nix/store/zcq4irdcgn3ljqdnlpm2zjp7f1kw9jvm-libX11-1.8.10' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.957769
#8 63.50 copying path '/nix/store/s3nhsilxmybnl8nn6vdnf1qi3p81415p-pcre2-10.44' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.957769
#8 63.51 copying path '/nix/store/anzlacc7ig1cq035xmwpliqnsjr5yd07-pcsclite-2.3.0-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.957769
#8 63.51 copying path '/nix/store/2w9j6vi0qhdyxjdkyf36lah23zpl3051-php-bcmath-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:44.957769
#8 63.59 copying path '/nix/store/ypwpd5qznmj896akf9hpryyiqig3drir-gnupg-2.4.5' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.069551
#8 63.61 copying path '/nix/store/b7svpr3lnh4m8zlchsjz3hs9i3ri1q3s-libxslt-1.1.42' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.069551
#8 63.61 copying path '/nix/store/dqf07dnn2li4z721flnsmmrnsk7n0s1h-php-calendar-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.069551
#8 63.61 copying path '/nix/store/mlvvxh0bgnj6p7rnpm11k3n7bl0fm9hs-php-ctype-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.069551
#8 63.66 copying path '/nix/store/ayicm0pi1iqs07inw9pblmfzhjhfbd2i-php-dom-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.069551
#8 63.66 copying path '/nix/store/hlyv3ypzy1asia2gj8f4qy6dnnprvdxm-php-exif-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.069551
#8 63.70 copying path '/nix/store/hk52s3rw8xf0dbrlbbvjgc4qp9hdmr3i-iptables-1.8.10' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.318805
#8 63.76 copying path '/nix/store/fp194a1rk74cik25dyylbgyrryll87s8-php-fileinfo-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.318805
#8 63.76 copying path '/nix/store/9g546ak9kcmm2a18vjaclb71p31zllmg-php-filter-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.318805
#8 63.77 copying path '/nix/store/fmhg1xw1nk77khakc359hyf3vnhnkr67-php-ftp-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.318805
#8 63.78 copying path '/nix/store/k1z2n1m93ni684a2j6s5ixxy5fcaq897-php-gettext-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.318805
#8 63.80 copying path '/nix/store/sz2pyj2wyb4swj2bbslj5nnhilq527wq-php-iconv-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.405487
#8 64.04 copying path '/nix/store/79xws3iflkk0cvnm3yam7jbw9mmj6qkr-php-mbstring-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.584262
#8 64.04 copying path '/nix/store/zzdk2b7kh3k02mw1j0snlzrnswp6w2n9-php-mysqli-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.584262
#8 64.09 copying path '/nix/store/mirrv7j19fb4g3xpl3g2d3z68ra4x3dd-php-pcntl-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.584262
#8 64.10 copying path '/nix/store/vniy1y5n8g28c55y7788npwc4h09fh7c-gnugrep-3.11' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.584262
#8 64.22 copying path '/nix/store/mildf9x8k10pqzgfm54xhnhwrlmcl953-libselinux-3.7' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.721901
#8 64.24 copying path '/nix/store/yq84qz7ak3xklafn4inawqz1vw3i4hn1-php-pdo-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.721901
#8 64.24 copying path '/nix/store/8ldz8y1fl60g7yv9a09b8zlgnskakv0w-php-pdo_mysql-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.721901
#8 64.27 copying path '/nix/store/l23hqzy592p5c0cnnbw3gj9izkmn1wps-php-posix-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.721901
#8 64.35 copying path '/nix/store/pz8n2xm9flh1g29dm7ksa54c45zydc62-php-session-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.899047
#8 64.38 copying path '/nix/store/jb170sz561ckx20k1yc87884yhxnwd7b-php-simplexml-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.899047
#8 64.42 copying path '/nix/store/vd7cv8925ix1k0807i3fymwxb0w6zqm0-php-soap-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.899047
#8 64.42 copying path '/nix/store/9bh2yw2fkyl5slfykyxd1cmg49rzwjq5-php-sockets-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:45.899047
#8 64.53 copying path '/nix/store/y1wxh6pds9xxnxvcgfzz8iz0zrss8g0x-php-sodium-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.013113
#8 64.59 copying path '/nix/store/31pf7m3fxyj7mlz9yr4n9l44lmx716ii-7zz-24.09' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.013113
#8 64.64 copying path '/nix/store/fk2psrg5k3i6z7az5p2bkjc5721hcglw-db-4.8.30' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.182052
#8 64.64 copying path '/nix/store/9ga2fkz4whkrnw0l7afpb1rsmbjxsk41-db-5.3.28' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.182052
#8 64.64 copying path '/nix/store/7pls6w3nqphhg33sfwr8yipdf4wlr1mp-gettext-0.21.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.182052
#8 64.71 copying path '/nix/store/cpyk8ksm7c79l0nabd576s80wngdzix5-gmp-with-cxx-6.3.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.182052
#8 64.81 copying path '/nix/store/aiw8lrc9f115z9fj6khijgk9frj7i772-icu4c-74.2' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.295156
#8 64.87 copying path '/nix/store/jav2ywbxm3ibmhfslc81sypqhssi3wvb-gmp-with-cxx-6.3.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.295156
#8 64.88 copying path '/nix/store/q5wfidiavi63f09b2lc54mnrgywplxyi-icu4c-73.2' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.295156
#8 64.92 copying path '/nix/store/17y91dqjg4wkafdz9swgm43az3sz42fh-lerc-4.0.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.415221
#8 64.93 copying path '/nix/store/nv1iyrrq6wk450qp41dgqqdvc4m51zdw-libvmaf-3.0.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.415221
#8 64.94 copying path '/nix/store/7918mvq3835h7aa8pa4icdq2q5c83p3d-libyuv-1787' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.415221
#8 65.05 copying path '/nix/store/wnl9qpnhayry14lhcbdafhadsjwsdr6p-patchelf-0.15.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.548233
#8 65.18 copying path '/nix/store/ybac75n8pll3xq09iz4jyccn7i8paiwb-libXpm-3.5.17' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.652076
#8 65.22 copying path '/nix/store/zj253anmmfqr6l5cp1l53qflkxr4cvv5-php-opcache-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.652076
#8 65.27 copying path '/nix/store/nxkf552xmhw7gpff39l541lzbql46x7k-nettle-3.10' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.652076
#8 65.28 copying path '/nix/store/cwd5g91gxm1jzi2a35xq96rz65kn2vpy-php-gmp-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.768198
#8 65.29 copying path '/nix/store/gfyyvw8ml3aw4pmar15j5vcld9w6ylbh-php-sysvsem-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.768198
#8 65.31 copying path '/nix/store/6wgd8c9vq93mqxzc7jhkl86mv6qbc360-coreutils-9.5' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.768198
#8 65.40 copying path '/nix/store/qyql0n9sh44n69c2398k0w66a9pwzl0l-php-tokenizer-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.873970
#8 65.51 copying path '/nix/store/y8vghanhgfjdzivp1p6d2fff5j8iw112-libaom-3.10.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.994852
#8 65.56 copying path '/nix/store/dvxbm47x918j56xz9bhqlj9pckbfm8if-libvmaf-3.0.0-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:46.994852
#8 65.62 copying path '/nix/store/cfhkzi86xpdq2grqvc2d3gspk9hp08kq-php-xml-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.150117
#8 65.78 copying path '/nix/store/637vq1nfs9s01qaa70av48cnc47vjk79-php-xmlreader-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.278238
#8 65.91 copying path '/nix/store/sl3fa5zh61xxl03m64if2wqzbvrb6zly-linux-pam-1.6.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.405197
#8 65.94 copying path '/nix/store/5jmqlg9z2shxrhadmbqppvignw23k0s7-php-xmlwriter-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.405197
#8 65.95 copying path '/nix/store/z815hr40d2sc1ngn2jfgsr257g7gxjk6-qrencode-4.1.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.405197
#8 65.95 copying path '/nix/store/8dzr2bndjm1rblw86fyha9wb1l2876cw-system-sendmail-1.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.405197
#8 65.95 copying path '/nix/store/qzpdmv5hnf52ln42hkbc874q37rrmbyy-systemd-minimal-libs-256.10' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.405197
#8 65.97 copying path '/nix/store/9hhs5ffbb2cl03bvzifi2a76xg2rf2wk-krb5-1.21.3-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.405197
#8 65.99 copying path '/nix/store/490zkpvg0q41z3937i1ifwp2ygwgkihf-openssl-3.3.2-bin' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.405197
#8 66.03 copying path '/nix/store/2rbjg2k2rv73vr8jm5avwrb5d77wzf7l-unbound-1.21.1-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.515140
#8 66.08 copying path '/nix/store/w02flr1vr42wcyvpw5nxbx3illkfas13-unixODBC-2.3.12' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.515140
#8 66.15 copying path '/nix/store/b4hmjzgk6w13525nwgb04xpmd71g4nbx-krb5-1.21.3-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.708217
#8 66.18 copying path '/nix/store/rc5j45brxiyl55fgd3adbcc43pdfr29v-openssl-3.3.2-bin' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.708217
#8 66.19 copying path '/nix/store/2h30fwd8w2s7xgf96gv334s9bbf78qyb-php-openssl-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.708217
#8 66.21 copying path '/nix/store/grpfa6f4czjg856b75xcsfan5qznxh6s-unzip-6.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.708217
#8 66.34 copying path '/nix/store/qmn58s05ay763vzi7vxax0a6kw37gyjq-util-linux-minimal-2.39.4-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.830712
#8 66.38 copying path '/nix/store/ih86hc1x4lx7dqrr3zppavw30s44bmf0-php-openssl-8.2.27-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.830712
#8 66.46 copying path '/nix/store/3sln66ij8pg114apkd8p6nr04y37q5z2-diffutils-3.10' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.955485
#8 66.53 copying path '/nix/store/r99d2m4swgmrv9jvm4l9di40hvanq1aq-findutils-4.10.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.955485
#8 66.53 copying path '/nix/store/smivcbakfgwin0d4518byn32rr6h31zg-util-linux-minimal-2.39.4-login' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.955485
#8 66.55 copying path '/nix/store/sc25r6y9q3b1ccjjj3fl79rdj6sjs91l-openssl-3.3.2-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:47.955485
#8 66.59 copying path '/nix/store/7ghsk2hik136m7m1rf8a8y7bd4qhycci-php-pdo_odbc-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.138378
#8 66.59 copying path '/nix/store/bmzls7bipx954xsnbgshh8kbi488zy5m-xz-5.6.3' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.138378
#8 66.59 copying path '/nix/store/l5g2v1jgfyf3j0jp9iv5b79fi8yrwzpp-zlib-1.3.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.138378
#8 66.62 copying path '/nix/store/74z4cw9g5fhzkhydpsaac9d41b073dy6-openssl-3.3.2-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.138378
#8 66.66 copying path '/nix/store/zkcf7gg7kdd7r1vxjmcb8ykcgs70wrqy-lvm2-2.03.27-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.138378
#8 66.67 copying path '/nix/store/fxkmblfp6k5fskjaxzkkhixh1xlrd6zj-zlib-ng-2.2.2' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.138378
#8 66.77 copying path '/nix/store/nmdfn7hkk0vvhqgacvsc8c8ls0qcrv64-binutils-2.43.1-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.279394
#8 66.84 copying path '/nix/store/qcf6xyjwapf5bp1p1dh75l6grfb7gl8v-cracklib-2.10.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.279394
#8 66.85 copying path '/nix/store/9wbpsj6ksd16x1qdqs29xli1dpz3fnl0-file-5.45' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.279394
#8 66.91 copying path '/nix/store/qkxw8vcbw4kysfjx1cpjl76x5pf005n1-cryptsetup-2.7.5' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.384289
#8 67.02 copying path '/nix/store/hcw121xziyns0lp35vfgqhygy80hdl6c-file-5.45' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.487167
#8 67.09 copying path '/nix/store/62qjb50708fdhb4f2y7zxyqr1afir4fk-gcc-13.3.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.487167
#8 67.10 copying path '/nix/store/xdxwr90hkhmy6kqnbkmz52p5y9ijhbbb-glib-2.82.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.487167
#8 67.12 copying path '/nix/store/1sjds2dmnrcfh6ni93v272y27mkf6aa2-gnutls-3.8.6' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.680679
#8 67.13 copying path '/nix/store/j3x77lgarc734is29divi4hj1grfv69c-kexec-tools-2.0.29' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.680679
#8 67.15 copying path '/nix/store/dak3fx41z361mc0axfwrzian4dg1djn6-krb5-1.21.3' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.680679
#8 67.15 copying path '/nix/store/7f8k48xpd2mzaz166azfn8sz5dqi2z33-libfido2-1.15.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.680679
#8 67.18 copying path '/nix/store/k23x1dins12p1iwazf709qm76c8rdw8r-cyrus-sasl-2.1.28' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.680679
#8 67.31 copying path '/nix/store/1mldak8ls62za08yckviy3zfpzim4aiz-libpng-apng-1.6.43' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.823030
#8 67.45 copying path '/nix/store/6sra1zhqva6z29dmp4xakylhdjrpf72k-libssh2-1.11.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.939410
#8 67.46 copying path '/nix/store/lsvh9q7b0qcjwxd7m2ihby4r3hn0dw9i-libssh2-1.11.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.953391
#8 67.49 copying path '/nix/store/vk4mlknqk9yjbqa68a7rvpfxfdw3rad7-binutils-2.43.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:48.953391
#8 67.57 copying path '/nix/store/0kkjw672jszig156v0a83a8cg0zxin8d-libzip-1.11.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:49.087012
#8 67.59 copying path '/nix/store/8lncw1wjkrywc0shnwhpsgwgj9cn4da1-nginx-1.26.2' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:49.087012
#8 67.63 copying path '/nix/store/5fhdd79mvzlhvkil71fz5l14ny22w2g2-freetype-2.13.3' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:49.087012
#8 67.72 copying path '/nix/store/gwp6yi4ywldkpd5cs4yky9kj7cc1dcrg-krb5-1.21.3-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:49.192304
#8 67.74 copying path '/nix/store/0hp36aakc6gswshgdzpvfvgb090fpj58-libwebp-1.4.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:49.192304
#8 67.82 copying path '/nix/store/mkhangs7d5gdl5r0mf7irylf7sldjkfx-openldap-2.6.9' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:49.365167
#8 67.84 copying path '/nix/store/ygz7vmxcmm3p9md71ck883qdlkfi7c62-perl-5.40.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:49.378565
#8 67.92 copying path '/nix/store/3m255gw7nh8hm91flbsp23v5fkpv1x8k-curl-8.11.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:49.378565
#8 68.00 copying path '/nix/store/pbwwimrnl5wzp17fyvxls48wx71i2agk-libssh2-1.11.1-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:49.466364
#8 68.10 copying path '/nix/store/vgr999zbckcbkvaybpbqn510xgjslcfl-php-imap-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:49.636103
#8 68.13 copying path '/nix/store/n753kvn23nh449ix430dxgwz2aznwbwl-libmicrohttpd-1.0.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:49.636103
#8 68.27 copying path '/nix/store/smdcnyx785fmr6z45j34xkba04wyq3ni-php-mysqlnd-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:49.772253
#8 68.40 copying path '/nix/store/sy1n9pdjhvvk5lkx07jz1ynacpw75w5v-libaom-3.10.0-bin' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:49.998080
#8 68.45 copying path '/nix/store/ky4chw5yg9n9rrljxaiahqmmvzrpds31-fontconfig-2.15.0-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.010587
#8 68.45 copying path '/nix/store/7z4p6h0y0n2ii1bn0ky3dgyvmhj3c3v2-php-zip-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.010587
#8 68.48 copying path '/nix/store/pz3d1dyrf30iz5q3lnqqr7fpbxzsc53b-php-zlib-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.010587
#8 68.63 copying path '/nix/store/z423par8g8w54awcns67lb0b6vdz1z7i-postgresql-16.6-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.198774
#8 68.68 copying path '/nix/store/8yff66km6d5mav6i2101kmvp78vgqfcc-readline-8.2p13' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.209625
#8 68.68 copying path '/nix/store/dsqzw96w4sxsp4q9yvkfl2yh701mpwgi-sqlite-3.46.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.209625
#8 68.71 copying path '/nix/store/2vfkdsxi4f7sgbzbmvhw5yhv9fgj7hqr-curl-8.11.1-bin' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.209625
#8 68.71 copying path '/nix/store/id0ns97w93xhwqi8rlwdnvy6zyhv04hn-util-linux-minimal-2.39.4-mount' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.209625
#8 68.83 copying path '/nix/store/fsh0l8jq8sjyh2a3kxd5v9wlpss745hb-util-linux-minimal-2.39.4-swap' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.337094
#8 68.92 copying path '/nix/store/4i4mjaf7z6gddspar487grxk5k1j4dcd-xz-5.6.3-bin' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.337094
#8 68.96 copying path '/nix/store/06q0p7bhn2ffxxya20rrfqdib3h32csn-zlib-1.3.1-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.497859
#8 69.13 copying path '/nix/store/83kw97s7widwn3rivl6gii26njlb5crs-libaom-3.10.0-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.600521
#8 69.14 copying path '/nix/store/afdaxacrxy5jgh5pmm3a2xn3hny6wdq7-zstd-1.5.6' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.600521
#8 69.23 copying path '/nix/store/zjgxcv2c3my7y99ykjxh98hnvnypbfsk-php-readline-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.790932
#8 69.23 copying path '/nix/store/2kfisxs701km8wfm0lmngsv66gg31b2v-php-pdo_pgsql-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.790932
#8 69.24 copying path '/nix/store/x9d49vaqlrkw97p9ichdwrnbh013kq7z-bash-interactive-5.2p37' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.825392
#8 69.25 copying path '/nix/store/h2lljkrv615081rk2d69j9mdr7sis7sj-php-pgsql-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.825392
#8 69.27 copying path '/nix/store/cf464y41p2x3lh1qvbg6678lc3f8zbd6-stdenv-linux' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.825392
#8 69.46 copying path '/nix/store/s569mjxw77rh0lwll868apq0xw5zlpyy-curl-8.11.1-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:50.981477
#8 69.61 copying path '/nix/store/1ryb89bvwapvi114wcgpcx5s7kibs8f8-php-ldap-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:51.178078
#8 69.81 copying path '/nix/store/n9xnph4ijzwr9pz4rk44gb362yh75mqh-php-pdo_sqlite-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:51.216751
2025-Jul-27 02:20:51.443474
#8 70.07 copying path '/nix/store/bfz2icynplvd84sjg7f9c8vjyv0brixk-php-sqlite3-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:51.697447
#8 70.33 building '/nix/store/7r2gqdwc4m6yykzghiz9j3d0jdwg5cjv-builder.pl.drv'...
2025-Jul-27 02:20:51.911559
#8 70.39 copying path '/nix/store/72hw5mjb97kyc3y1jfla1q65i08aqpi0-curl-8.11.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:51.911559
#8 70.39 copying path '/nix/store/h16ifxs07jpm4gj6a4gwi1zx66yvl2mw-kmod-31' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:51.911559
#8 70.40 copying path '/nix/store/y8k4wbgnkvdr7k10irr73s6ynjd9jg3p-kmod-31-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:51.911559
#8 70.40 copying path '/nix/store/pqjhj3mbzswhajlbf3nb5m6z06b6bqv6-libarchive-3.7.7-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:51.911559
#8 70.40 copying path '/nix/store/nwn9micsii2qax3bfx9yr48276163bqg-libtiff-4.7.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:51.911559
#8 70.41 copying path '/nix/store/qpmg2piws75wmy1619ryh0dhwi9638ni-zstd-1.5.6-bin' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:51.911559
#8 70.54 copying path '/nix/store/qxwppk9r13zqn5rp13h7f1zc8b4xqfbw-libpwquality-1.4.5-lib' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:52.304987
#8 70.94 copying path '/nix/store/a0fbsn0xx6c1zviv2y24s2ryy2jdj6k0-kbd-2.6.4' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:52.422833
#8 70.99 copying path '/nix/store/qyw4nyrsvr43cd6p6nky2134raf3r02s-gdk-pixbuf-2.42.12' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:52.422833
#8 71.00 copying path '/nix/store/6y1gr6bh62cxfl6wh46hqjjmys5s2mry-libwebp-1.4.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:52.422833
#8 71.05 copying path '/nix/store/3nf9cc1p9d0jw97sf9racacnz5vyq2d0-curl-8.11.1-bin' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:52.580248
#8 71.06 copying path '/nix/store/m9lims7vfas44fh5kg2dc8lz09wwbk9i-elfutils-0.191' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:52.580248
#8 71.06 copying path '/nix/store/bkrsswbcbj0isdk1nwdqj1sn9jijlms5-git-minimal-2.47.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:52.580248
#8 71.06 copying path '/nix/store/mijvnwpkic0w59adx8gpds5wxi3plf07-mariadb-connector-c-3.3.5' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:52.580248
#8 71.06 copying path '/nix/store/d21n7nqxhkpd4cskh8i612mrv96kyak9-php-curl-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:52.580248
#8 71.06 copying path '/nix/store/mm0hlk3mfzddlcgk4235lxbi2fp1gn77-tpm2-tss-4.1.3' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:53.418527
#8 72.05 building '/nix/store/h6lfwbf28y4hz6rh078jpmwawcgd5nv0-npm-9.9.4.tgz.drv'...
2025-Jul-27 02:20:53.520406
#8 72.07 copying path '/nix/store/fwgfjb113gr2a5cw1lnkc4f48ccjrzd7-libbpf-1.4.7' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:53.520406
#8 72.15 copying path '/nix/store/a0bbn7qyxgyh4jhf9dgwq3l6j12qpn4f-libavif-1.1.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:53.651094
#8 72.28 copying path '/nix/store/5g9b8p16cbyhg36qiajijp6gkv944hp2-gd-2.3.3' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:53.868146
#8 72.37 copying path '/nix/store/13ar1lxax1zrnl2fwim2gc06a1fij2a8-php-gd-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:53.868146
#8 72.50 copying path '/nix/store/bl5dgjbbr9y4wpdw6k959mkq4ig0jwyg-systemd-256.10' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:54.076036
#8 72.71 copying path '/nix/store/qlzvmgr8w9prdlyys7irqf86p7bndf5b-binutils-wrapper-2.43.1' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:54.404135
#8 73.03 copying path '/nix/store/26b79gadv22qirdkl82pnx58z19x8p1r-icu4c-74.2-dev' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:54.586260
#8 73.22 copying path '/nix/store/fl07kp4ckh8fnylwv7z8yy7gym4mbab3-php-intl-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:54.737087
#8 73.37 copying path '/nix/store/iqip6spnxj3696lmmr9gdxrk3z54n7dw-php-extra-init-8.2.27.ini' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:55.004336
#8 73.64 copying path '/nix/store/lib106in05zrixvpgm5jxg8jsasrcpcc-nodejs-18.20.5' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:56.348725
#8 74.98
2025-Jul-27 02:20:56.516572
#8 74.98 trying https://registry.npmjs.org/npm/-/npm-9.9.4.tgz
2025-Jul-27 02:20:56.528556
#8 75.02   % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
2025-Jul-27 02:20:56.528556
#8 75.03                                  Dload  Upload   Total   Spent    Left  Speed
2025-Jul-27 02:20:56.528556
#8 75.15 100 2648k  100 2648k    0     0  20.5M      0 --:--:-- --:--:-- --:--:-- 20.6M
2025-Jul-27 02:20:56.888746
#8 75.52 building '/nix/store/cnhv5ydyaxsw8dhvydc5jm4pkrzrgjq4-libraries.drv'...
2025-Jul-27 02:20:57.994914
#8 76.63 building '/nix/store/j41ay1gsqacza8zmgssm56ny9szsc1r6-php-extra-init-8.2.27.ini.drv'...
2025-Jul-27 02:20:58.180785
#8 76.66 copying path '/nix/store/nv7agllrn6xvfyxqjg55l2nj3n18sgqc-php-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:20:58.693238
#8 77.32 building '/nix/store/gx837sa0kcz1lzycss0sa8alab34ib7h-e24b4c09e963677b1beea49d411cd315a024ad3a-env.drv'...
2025-Jul-27 02:20:59.947033
#8 78.58 copying path '/nix/store/a0qcy2m5mqwrlkr3xkza0n2wdmbq8myq-php-with-extensions-8.2.27' from 'https://cache.nixos.org'...
2025-Jul-27 02:21:00.174158
#8 78.81 copying path '/nix/store/rbbaws9k85zah0d77ggsf3hnsd8cavdd-composer-2.8.4' from 'https://cache.nixos.org'...
2025-Jul-27 02:21:03.013257
#8 81.64 copying path '/nix/store/888bkaqdpfpx72dd8bdc69qsqlgbhcvf-gcc-wrapper-13.3.0' from 'https://cache.nixos.org'...
2025-Jul-27 02:21:03.245119
#8 81.66 copying path '/nix/store/vi2j2pqg0vcwrq9nv1lcxqr9czfbfza2-make-binary-wrapper-hook' from 'https://cache.nixos.org'...
2025-Jul-27 02:21:03.245119
#8 81.66 copying path '/nix/store/lzrs17sc8bhi87nb1y1q1bas73j6q10y-stdenv-linux' from 'https://cache.nixos.org'...
2025-Jul-27 02:21:03.245119
#8 81.71 building '/nix/store/x5rljg8j5a2x0vdiklz6dz3qc4k49zaj-php-with-extensions-8.2.27.drv'...
2025-Jul-27 02:21:03.861570
#8 82.49 building '/nix/store/a32hqx0622bhnzcjxh3csqswnsycgm6m-npm.drv'...
2025-Jul-27 02:21:03.873640
2025-Jul-27 02:21:04.091265
#8 82.57 Running phase: unpackPhase
2025-Jul-27 02:21:04.091265
#8 82.57 unpacking source archive /nix/store/fkd1ma3nify8r9wp463yg5rqz9hdcyf1-npm-9.9.4.tgz
2025-Jul-27 02:21:04.349934
#8 82.98 source root is package
2025-Jul-27 02:21:04.594819
#8 83.07 setting SOURCE_DATE_EPOCH to timestamp 499162500 of file package/package.json
2025-Jul-27 02:21:04.594819
#8 83.07 Running phase: installPhase
2025-Jul-27 02:21:05.521177
#8 84.15 building '/nix/store/20wn0b6rjdaxgambrw1d82xls6prv1g5-e24b4c09e963677b1beea49d411cd315a024ad3a-env.drv'...
2025-Jul-27 02:21:05.650426
#8 84.28 created 175 symlinks in user environment
2025-Jul-27 02:21:05.896667
#8 84.38 building '/nix/store/l8jij9f813i3mpnnb25n5wcs4pllvpra-user-environment.drv'...
2025-Jul-27 02:21:06.148376
#8 84.78 removing old generations of profile /nix/var/nix/profiles/per-user/root/profile
2025-Jul-27 02:21:06.302822
#8 84.78 removing profile version 1
2025-Jul-27 02:21:06.302822
#8 84.78 removing old generations of profile /nix/var/nix/profiles/per-user/root/channels
2025-Jul-27 02:21:06.302822
#8 84.78 removing old generations of profile /nix/var/nix/profiles/per-user/root/profile
2025-Jul-27 02:21:06.302822
#8 84.78 removing old generations of profile /nix/var/nix/profiles/per-user/root/channels
2025-Jul-27 02:21:06.302822
#8 84.79 finding garbage collector roots...
2025-Jul-27 02:21:06.302822
#8 84.79 removing stale link from '/nix/var/nix/gcroots/auto/lzjbmb2ry0z7lma2fvpqprb12921pnb5' to '/nix/var/nix/profiles/per-user/root/profile-1-link'
2025-Jul-27 02:21:06.302822
#8 84.80 deleting garbage...
2025-Jul-27 02:21:06.302822
#8 84.81 deleting '/nix/store/ir9fki7838bmk4hlj0zmwbw45q101j66-user-environment.drv'
2025-Jul-27 02:21:06.302822
#8 84.81 deleting '/nix/store/b9rj4wk1cxh7g2ib89aqbcapzzar8p2s-user-environment'
2025-Jul-27 02:21:06.302822
#8 84.82 deleting '/nix/store/xxyn8jfxcpr5ac9dvismfzx39ijh9kiv-env-manifest.nix'
2025-Jul-27 02:21:06.302822
#8 84.93 deleting '/nix/store/na4c03201p0gmhn3bqr089x0xqia157w-source'
2025-Jul-27 02:21:06.481826
#8 84.94 deleting '/nix/store/vi2j2pqg0vcwrq9nv1lcxqr9czfbfza2-make-binary-wrapper-hook'
2025-Jul-27 02:21:06.498309
#8 84.94 deleting '/nix/store/lzrs17sc8bhi87nb1y1q1bas73j6q10y-stdenv-linux'
2025-Jul-27 02:21:06.498309
#8 84.95 deleting '/nix/store/888bkaqdpfpx72dd8bdc69qsqlgbhcvf-gcc-wrapper-13.3.0'
2025-Jul-27 02:21:06.498309
#8 84.95 deleting '/nix/store/qlzvmgr8w9prdlyys7irqf86p7bndf5b-binutils-wrapper-2.43.1'
2025-Jul-27 02:21:06.498309
#8 84.96 deleting '/nix/store/vk4mlknqk9yjbqa68a7rvpfxfdw3rad7-binutils-2.43.1'
2025-Jul-27 02:21:06.498309
#8 84.99 deleting '/nix/store/62qjb50708fdhb4f2y7zxyqr1afir4fk-gcc-13.3.0'
2025-Jul-27 02:21:06.498309
#8 85.11 deleting '/nix/store/lcxvgkg659vbvdq86mhxa599wn48f35c-glibc-2.40-36-dev'
2025-Jul-27 02:21:06.666245
#8 85.18 deleting '/nix/store/29mb4q8b5306f4gk2wh38h0c1akb0n97-glibc-2.40-36-bin'
2025-Jul-27 02:21:06.666245
#8 85.19 deleting '/nix/store/dvsai0ym9czjl5mcsarcdwccb70615n4-linux-headers-6.10'
2025-Jul-27 02:21:06.666245
#8 85.30 deleting '/nix/store/fkd1ma3nify8r9wp463yg5rqz9hdcyf1-npm-9.9.4.tgz'
2025-Jul-27 02:21:06.882065
#8 85.30 deleting '/nix/store/cf464y41p2x3lh1qvbg6678lc3f8zbd6-stdenv-linux'
2025-Jul-27 02:21:06.882065
#8 85.30 deleting '/nix/store/3sln66ij8pg114apkd8p6nr04y37q5z2-diffutils-3.10'
2025-Jul-27 02:21:06.882065
#8 85.32 deleting '/nix/store/rfrjws98w6scfx7m63grb0m6sg925ahd-patch-2.7.6'
2025-Jul-27 02:21:06.882065
#8 85.32 deleting '/nix/store/b07fkj0ib4wcv6n0bcxgzmqxkwjq6fv1-ed-1.20.2'
2025-Jul-27 02:21:06.882065
#8 85.32 deleting '/nix/store/s569mjxw77rh0lwll868apq0xw5zlpyy-curl-8.11.1-dev'
2025-Jul-27 02:21:06.882065
#8 85.32 deleting '/nix/store/gwp6yi4ywldkpd5cs4yky9kj7cc1dcrg-krb5-1.21.3-dev'
2025-Jul-27 02:21:06.882065
#8 85.33 deleting '/nix/store/hjirnxx1nm49n3prqjmm3x1lax55hky4-curl-8.11.1-man'
2025-Jul-27 02:21:06.882065
#8 85.33 deleting '/nix/store/r99d2m4swgmrv9jvm4l9di40hvanq1aq-findutils-4.10.0'
2025-Jul-27 02:21:06.882065
#8 85.34 deleting '/nix/store/pbwwimrnl5wzp17fyvxls48wx71i2agk-libssh2-1.11.1-dev'
2025-Jul-27 02:21:06.882065
#8 85.34 deleting '/nix/store/sc25r6y9q3b1ccjjj3fl79rdj6sjs91l-openssl-3.3.2-dev'
2025-Jul-27 02:21:06.882065
#8 85.35 deleting '/nix/store/wnl9qpnhayry14lhcbdafhadsjwsdr6p-patchelf-0.15.0'
2025-Jul-27 02:21:06.882065
#8 85.36 deleting '/nix/store/mxi53f9xhkshf3la64awqacfmx9ppkzw-die-hook'
2025-Jul-27 02:21:06.882065
#8 85.36 deleting '/nix/store/v1h6rvf3ly2s0zwxsck34bfy7gbrfbcj-nghttp2-1.64.0-dev'
2025-Jul-27 02:21:06.882065
#8 85.36 deleting '/nix/store/w75dbhs3yxq98309az0ybgrg2afq8g1p-libmpc-1.3.1'
2025-Jul-27 02:21:06.882065
#8 85.36 deleting '/nix/store/simak2bypwin0skwr4p9pbgccnyi1jph-mpfr-4.2.1'
2025-Jul-27 02:21:06.882065
#8 85.36 deleting '/nix/store/ygz7vmxcmm3p9md71ck883qdlkfi7c62-perl-5.40.0'
2025-Jul-27 02:21:06.924942
#8 85.56 deleting '/nix/store/lfwvcxbhmqnpmqcxbpd5bwx3bhkpjgci-mirrors-list'
2025-Jul-27 02:21:06.941302
2025-Jul-27 02:21:07.147582
#8 85.56 deleting '/nix/store/2vfkdsxi4f7sgbzbmvhw5yhv9fgj7hqr-curl-8.11.1-bin'
2025-Jul-27 02:21:07.147582
#8 85.56 deleting '/nix/store/3m255gw7nh8hm91flbsp23v5fkpv1x8k-curl-8.11.1'
2025-Jul-27 02:21:07.147582
#8 85.56 deleting '/nix/store/4xpviqy8kghz7410p5aagdhykcjcpnd1-expand-response-params'
2025-Jul-27 02:21:07.147582
#8 85.57 deleting '/nix/store/9wbpsj6ksd16x1qdqs29xli1dpz3fnl0-file-5.45'
2025-Jul-27 02:21:07.147582
#8 85.57 deleting '/nix/store/490zkpvg0q41z3937i1ifwp2ygwgkihf-openssl-3.3.2-bin'
2025-Jul-27 02:21:07.147582
#8 85.57 deleting '/nix/store/nmdfn7hkk0vvhqgacvsc8c8ls0qcrv64-binutils-2.43.1-lib'
2025-Jul-27 02:21:07.147582
#8 85.58 deleting '/nix/store/5b4s9pww5j7s6wnh6l953pizyp9za2wl-nghttp2-1.64.0-lib'
2025-Jul-27 02:21:07.147582
#8 85.58 deleting '/nix/store/sbr9bjn36i9ibh5hmdni1p3nrlbkm7qc-lndir-1.0.5'
2025-Jul-27 02:21:07.147582
#8 85.58 deleting '/nix/store/gqa5wiphbpgxnqhn33mkj0j7wvj3651s-nghttp2-1.64.0'
2025-Jul-27 02:21:07.147582
#8 85.58 deleting '/nix/store/dak3fx41z361mc0axfwrzian4dg1djn6-krb5-1.21.3'
2025-Jul-27 02:21:07.147582
#8 85.59 deleting '/nix/store/9hhs5ffbb2cl03bvzifi2a76xg2rf2wk-krb5-1.21.3-lib'
2025-Jul-27 02:21:07.147582
#8 85.59 deleting '/nix/store/6sra1zhqva6z29dmp4xakylhdjrpf72k-libssh2-1.11.1'
2025-Jul-27 02:21:07.147582
#8 85.59 deleting '/nix/store/idpsjqf760ap2vr49hkdshhj8sfdc6mn-isl-0.20'
2025-Jul-27 02:21:07.147582
#8 85.60 deleting '/nix/store/scgfwh3z1s3l2vhvyjsgfgx5ql552sls-gawk-5.3.1'
2025-Jul-27 02:21:07.147582
#8 85.61 deleting '/nix/store/kzzpf6mzrrg2wjf8xqsxy6b2q8jkl2f5-libraries'
2025-Jul-27 02:21:07.147582
#8 85.61 deleting '/nix/store/q0plci6b8x9m08k61jg89dc77hlhmghz-openssl-3.3.2'
2025-Jul-27 02:21:07.147582
#8 85.61 deleting '/nix/store/ap724yhgv28mpsi1mmqcwypj4rrfhqmg-update-autotools-gnu-config-scripts-hook'
2025-Jul-27 02:21:07.147582
#8 85.61 deleting '/nix/store/0yl1wf4jim6830k2m3c3v5kyp2l9z8ay-gnu-config-2024-01-01'
2025-Jul-27 02:21:07.147582
#8 85.61 deleting '/nix/store/58sa2xbrld24c6sgckiwf9gg3sp3zlz2-builder.pl'
2025-Jul-27 02:21:07.147582
#8 85.62 deleting '/nix/store/kwq87k2nykp8fkw8jgqav4jl5nw65isv-gmp-6.3.0'
2025-Jul-27 02:21:07.147582
#8 85.62 deleting '/nix/store/hbzw8k8ygv6bfzvsvnd4gb8qmm8xjbvn-gnumake-4.4.1'
2025-Jul-27 02:21:07.147582
#8 85.62 deleting '/nix/store/awsvw44jla0idziiks2zwgzslfd2dczn-source'
2025-Jul-27 02:21:13.523837
#8 92.15 deleting unused links...
2025-Jul-27 02:21:13.721689
#8 92.16 note: currently hard linking saves -0.00 MiB
2025-Jul-27 02:21:13.721689
#8 92.20 53 store paths deleted, 534.01 MiB freed
2025-Jul-27 02:21:13.808330
#8 DONE 92.4s
2025-Jul-27 02:21:13.968389
#9 [ 5/13] RUN sudo apt-get update && sudo apt-get install -y --no-install-recommends curl wget
2025-Jul-27 02:21:14.055721
#9 0.242 Get:1 http://archive.ubuntu.com/ubuntu jammy InRelease [270 kB]
2025-Jul-27 02:21:14.227745
#9 0.300 Get:2 http://archive.ubuntu.com/ubuntu jammy-updates InRelease [128 kB]
2025-Jul-27 02:21:14.227745
#9 0.301 Get:3 http://archive.ubuntu.com/ubuntu jammy-backports InRelease [127 kB]
2025-Jul-27 02:21:14.227745
#9 0.415 Get:4 http://archive.ubuntu.com/ubuntu jammy/universe amd64 Packages [17.5 MB]
2025-Jul-27 02:21:14.335294
#9 0.523 Get:5 http://security.ubuntu.com/ubuntu jammy-security InRelease [129 kB]
2025-Jul-27 02:21:14.353443
2025-Jul-27 02:21:14.483077
#9 0.671 Get:6 http://archive.ubuntu.com/ubuntu jammy/multiverse amd64 Packages [266 kB]
2025-Jul-27 02:21:14.620153
#9 0.681 Get:7 http://archive.ubuntu.com/ubuntu jammy/restricted amd64 Packages [164 kB]
2025-Jul-27 02:21:14.620153
#9 0.686 Get:8 http://archive.ubuntu.com/ubuntu jammy/main amd64 Packages [1792 kB]
2025-Jul-27 02:21:14.620153
#9 0.718 Get:9 http://archive.ubuntu.com/ubuntu jammy-updates/universe amd64 Packages [1574 kB]
2025-Jul-27 02:21:14.620153
#9 0.744 Get:10 http://archive.ubuntu.com/ubuntu jammy-updates/multiverse amd64 Packages [75.9 kB]
2025-Jul-27 02:21:14.620153
#9 0.747 Get:11 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 Packages [3471 kB]
2025-Jul-27 02:21:14.620153
#9 0.807 Get:12 http://archive.ubuntu.com/ubuntu jammy-updates/restricted amd64 Packages [5163 kB]
2025-Jul-27 02:21:14.728215
#9 0.915 Get:13 http://archive.ubuntu.com/ubuntu jammy-backports/universe amd64 Packages [35.2 kB]
2025-Jul-27 02:21:14.893926
#9 0.931 Get:14 http://archive.ubuntu.com/ubuntu jammy-backports/main amd64 Packages [83.2 kB]
2025-Jul-27 02:21:14.930911
#9 1.118 Get:15 http://security.ubuntu.com/ubuntu jammy-security/multiverse amd64 Packages [48.5 kB]
2025-Jul-27 02:21:15.054303
#9 1.138 Get:16 http://security.ubuntu.com/ubuntu jammy-security/main amd64 Packages [3160 kB]
2025-Jul-27 02:21:15.081622
#9 1.241 Get:17 http://security.ubuntu.com/ubuntu jammy-security/universe amd64 Packages [1269 kB]
2025-Jul-27 02:21:15.257411
#9 1.293 Get:18 http://security.ubuntu.com/ubuntu jammy-security/restricted amd64 Packages [4976 kB]
2025-Jul-27 02:21:16.737081
#9 2.924 Fetched 40.2 MB in 3s (14.8 MB/s)
2025-Jul-27 02:21:16.737081
#9 2.924 Reading package lists...
2025-Jul-27 02:21:18.026004
2025-Jul-27 02:21:18.243740
#9 4.281 Reading package lists...
2025-Jul-27 02:21:19.292456
2025-Jul-27 02:21:19.466270
#9 5.503 Building dependency tree...
2025-Jul-27 02:21:19.541650
2025-Jul-27 02:21:19.695145
#9 5.731 Reading state information...
2025-Jul-27 02:21:19.746884
#9 5.934 curl is already the newest version (7.81.0-1ubuntu1.20).
2025-Jul-27 02:21:19.746884
#9 5.934 The following NEW packages will be installed:
2025-Jul-27 02:21:19.898333
#9 5.936   wget
2025-Jul-27 02:21:19.973881
#9 6.161 0 upgraded, 1 newly installed, 0 to remove and 29 not upgraded.
2025-Jul-27 02:21:19.973881
#9 6.161 Need to get 339 kB of archives.
2025-Jul-27 02:21:19.973881
#9 6.161 After this operation, 950 kB of additional disk space will be used.
2025-Jul-27 02:21:19.973881
#9 6.161 Get:1 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 wget amd64 1.21.2-2ubuntu1.1 [339 kB]
2025-Jul-27 02:21:20.595898
#9 6.783 debconf: delaying package configuration, since apt-utils is not installed
2025-Jul-27 02:21:20.721250
#9 6.818 Fetched 339 kB in 1s (495 kB/s)
2025-Jul-27 02:21:20.721250
#9 6.839 Selecting previously unselected package wget.
2025-Jul-27 02:21:20.721250
#9 6.839 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 10745 files and directories currently installed.)
2025-Jul-27 02:21:20.730811
#9 6.874 Preparing to unpack .../wget_1.21.2-2ubuntu1.1_amd64.deb ...
2025-Jul-27 02:21:20.730811
#9 6.877 Unpacking wget (1.21.2-2ubuntu1.1) ...
2025-Jul-27 02:21:20.730811
#9 6.908 Setting up wget (1.21.2-2ubuntu1.1) ...
2025-Jul-27 02:21:20.839269
#9 DONE 7.0s
2025-Jul-27 02:21:20.839269
2025-Jul-27 02:21:20.839269
#10 [ 6/13] COPY .nixpacks/assets /assets/
2025-Jul-27 02:21:20.839269
#10 DONE 0.0s
2025-Jul-27 02:21:20.990124
#11 [ 7/13] COPY . /app/.
2025-Jul-27 02:21:27.251831
#11 DONE 6.4s
2025-Jul-27 02:21:27.403156
#12 [ 8/13] RUN  mkdir -p /var/log/nginx && mkdir -p /var/cache/nginx
2025-Jul-27 02:21:27.418180
#12 DONE 0.2s
2025-Jul-27 02:21:27.418180
2025-Jul-27 02:21:27.418180
#13 [ 9/13] RUN  composer install --ignore-platform-reqs
2025-Jul-27 02:21:27.828299
#13 0.415 Installing dependencies from lock file (including require-dev)
2025-Jul-27 02:21:27.927832
#13 0.434 Verifying lock file contents can be installed on current platform.
2025-Jul-27 02:21:27.927832
#13 0.478 Package operations: 213 installs, 0 updates, 0 removals
2025-Jul-27 02:21:27.927832
#13 0.482   - Downloading wikimedia/composer-merge-plugin (v2.1.0)
2025-Jul-27 02:21:27.927832
#13 0.486   - Downloading aws/aws-crt-php (v1.2.7)
2025-Jul-27 02:21:27.927832
#13 0.486   - Downloading symfony/finder (v7.2.2)
2025-Jul-27 02:21:27.927832
#13 0.487   - Downloading symfony/polyfill-mbstring (v1.31.0)
2025-Jul-27 02:21:27.927832
#13 0.488   - Downloading symfony/var-dumper (v7.2.3)
2025-Jul-27 02:21:27.927832
#13 0.489   - Downloading psr/log (3.0.2)
2025-Jul-27 02:21:27.927832
#13 0.489   - Downloading php-debugbar/php-debugbar (v2.1.6)
2025-Jul-27 02:21:27.927832
#13 0.490   - Downloading voku/portable-ascii (2.0.3)
2025-Jul-27 02:21:27.927832
#13 0.492   - Downloading symfony/polyfill-php80 (v1.31.0)
2025-Jul-27 02:21:27.927832
#13 0.492   - Downloading symfony/polyfill-ctype (v1.31.0)
2025-Jul-27 02:21:27.927832
#13 0.492   - Downloading phpoption/phpoption (1.9.3)
2025-Jul-27 02:21:27.927832
#13 0.492   - Downloading graham-campbell/result-type (v1.1.3)
2025-Jul-27 02:21:27.927832
#13 0.493   - Downloading vlucas/phpdotenv (v5.6.2)
2025-Jul-27 02:21:27.927832
#13 0.493   - Downloading symfony/css-selector (v7.2.0)
2025-Jul-27 02:21:27.927832
#13 0.494   - Downloading tijsverkoyen/css-to-inline-styles (v2.3.0)
2025-Jul-27 02:21:27.927832
#13 0.494   - Downloading symfony/polyfill-uuid (v1.31.0)
2025-Jul-27 02:21:27.927832
#13 0.495   - Downloading symfony/uid (v7.2.0)
2025-Jul-27 02:21:27.927832
#13 0.496   - Downloading symfony/deprecation-contracts (v3.5.1)
2025-Jul-27 02:21:27.927832
#13 0.496   - Downloading symfony/routing (v7.2.3)
2025-Jul-27 02:21:27.927832
#13 0.496   - Downloading symfony/process (v7.2.5)
2025-Jul-27 02:21:27.927832
#13 0.496   - Downloading symfony/polyfill-php83 (v1.31.0)
2025-Jul-27 02:21:27.927832
#13 0.497   - Downloading symfony/polyfill-intl-normalizer (v1.31.0)
2025-Jul-27 02:21:27.927832
#13 0.498   - Downloading symfony/polyfill-intl-idn (v1.31.0)
2025-Jul-27 02:21:27.927832
#13 0.498   - Downloading symfony/mime (v7.2.4)
2025-Jul-27 02:21:27.927832
#13 0.498   - Downloading psr/container (2.0.2)
2025-Jul-27 02:21:27.927832
#13 0.498   - Downloading symfony/service-contracts (v3.5.1)
2025-Jul-27 02:21:27.927832
#13 0.498   - Downloading psr/event-dispatcher (1.0.0)
2025-Jul-27 02:21:27.927832
#13 0.499   - Downloading symfony/event-dispatcher-contracts (v3.5.1)
2025-Jul-27 02:21:27.927832
#13 0.499   - Downloading symfony/event-dispatcher (v7.2.0)
2025-Jul-27 02:21:27.927832
#13 0.501   - Downloading doctrine/lexer (3.0.1)
2025-Jul-27 02:21:27.927832
#13 0.501   - Downloading egulias/email-validator (4.0.4)
2025-Jul-27 02:21:27.927832
#13 0.501   - Downloading symfony/mailer (v7.2.3)
2025-Jul-27 02:21:27.927832
#13 0.503   - Downloading symfony/http-foundation (v7.2.5)
2025-Jul-27 02:21:27.927832
#13 0.504   - Downloading symfony/error-handler (v7.2.5)
2025-Jul-27 02:21:27.927832
#13 0.505   - Downloading symfony/http-kernel (v7.2.5)
2025-Jul-27 02:21:27.927832
#13 0.505   - Downloading symfony/polyfill-intl-grapheme (v1.31.0)
2025-Jul-27 02:21:27.927832
#13 0.507   - Downloading symfony/string (v7.2.0)
2025-Jul-27 02:21:27.927832
#13 0.507   - Downloading symfony/console (v7.2.5)
2025-Jul-27 02:21:27.927832
#13 0.509   - Downloading ramsey/collection (2.1.1)
2025-Jul-27 02:21:27.927832
#13 0.509   - Downloading brick/math (0.12.3)
2025-Jul-27 02:21:27.927832
#13 0.510   - Downloading ramsey/uuid (4.7.6)
2025-Jul-27 02:21:27.927832
#13 0.511   - Downloading psr/simple-cache (3.0.0)
2025-Jul-27 02:21:27.927832
#13 0.512   - Downloading nunomaduro/termwind (v2.3.0)
2025-Jul-27 02:21:27.927832
#13 0.512   - Downloading symfony/translation-contracts (v3.5.1)
2025-Jul-27 02:21:27.927832
#13 0.513   - Downloading symfony/translation (v7.2.4)
2025-Jul-27 02:21:27.927832
#13 0.515   - Downloading psr/clock (1.0.0)
2025-Jul-27 02:21:27.927832
#13 0.515   - Downloading symfony/clock (v7.2.0)
2025-Jul-27 02:21:27.927832
#13 0.515   - Downloading carbonphp/carbon-doctrine-types (3.2.0)
2025-Jul-27 02:21:27.927832
#13 0.515   - Downloading nesbot/carbon (3.9.1)
2025-Jul-27 02:21:27.927832
#13 0.516   - Downloading monolog/monolog (3.9.0)
2025-Jul-27 02:21:28.032340
#13 0.517   - Downloading psr/http-message (2.0)
2025-Jul-27 02:21:28.032340
#13 0.518   - Downloading psr/http-factory (1.1.0)
2025-Jul-27 02:21:28.032340
#13 0.519   - Downloading league/uri-interfaces (7.5.0)
2025-Jul-27 02:21:28.032340
#13 0.522   - Downloading league/uri (7.5.1)
2025-Jul-27 02:21:28.032340
#13 0.522   - Downloading league/mime-type-detection (1.16.0)
2025-Jul-27 02:21:28.032340
#13 0.522   - Downloading league/flysystem-local (3.29.0)
2025-Jul-27 02:21:28.032340
#13 0.522   - Downloading league/flysystem (3.29.1)
2025-Jul-27 02:21:28.032340
#13 0.522   - Downloading nette/utils (v4.0.6)
2025-Jul-27 02:21:28.032340
#13 0.524   - Downloading nette/schema (v1.3.2)
2025-Jul-27 02:21:28.032340
#13 0.524   - Downloading dflydev/dot-access-data (v3.0.3)
2025-Jul-27 02:21:28.032340
#13 0.525   - Downloading league/config (v1.2.0)
2025-Jul-27 02:21:28.032340
#13 0.527   - Downloading league/commonmark (2.6.2)
2025-Jul-27 02:21:28.032340
#13 0.528   - Downloading laravel/serializable-closure (v2.0.4)
2025-Jul-27 02:21:28.032340
#13 0.528   - Downloading laravel/prompts (v0.3.5)
2025-Jul-27 02:21:28.032340
#13 0.529   - Downloading guzzlehttp/uri-template (v1.0.4)
2025-Jul-27 02:21:28.032340
#13 0.529   - Downloading psr/http-client (1.0.3)
2025-Jul-27 02:21:28.032340
#13 0.532   - Downloading ralouphie/getallheaders (3.0.3)
2025-Jul-27 02:21:28.032340
#13 0.532   - Downloading guzzlehttp/psr7 (2.7.1)
2025-Jul-27 02:21:28.032340
#13 0.532   - Downloading guzzlehttp/promises (2.2.0)
2025-Jul-27 02:21:28.032340
#13 0.532   - Downloading guzzlehttp/guzzle (7.9.3)
2025-Jul-27 02:21:28.032340
#13 0.532   - Downloading fruitcake/php-cors (v1.3.0)
2025-Jul-27 02:21:28.032340
#13 0.532   - Downloading webmozart/assert (1.11.0)
2025-Jul-27 02:21:28.032340
#13 0.533   - Downloading dragonmantank/cron-expression (v3.4.0)
2025-Jul-27 02:21:28.032340
#13 0.534   - Downloading doctrine/inflector (2.0.10)
2025-Jul-27 02:21:28.032340
#13 0.536   - Downloading laravel/framework (v12.12.0)
2025-Jul-27 02:21:28.032340
#13 0.543   - Downloading barryvdh/laravel-debugbar (v3.15.4)
2025-Jul-27 02:21:28.032340
#13 0.543   - Downloading masterminds/html5 (2.9.0)
2025-Jul-27 02:21:28.032340
#13 0.543   - Downloading sabberworm/php-css-parser (v8.8.0)
2025-Jul-27 02:21:28.032340
#13 0.543   - Downloading dompdf/php-svg-lib (1.0.0)
2025-Jul-27 02:21:28.032340
#13 0.547   - Downloading dompdf/php-font-lib (1.0.1)
2025-Jul-27 02:21:28.032340
#13 0.547   - Downloading dompdf/dompdf (v3.1.0)
2025-Jul-27 02:21:28.032340
#13 0.548   - Downloading barryvdh/laravel-dompdf (v3.1.1)
2025-Jul-27 02:21:28.032340
#13 0.549   - Downloading botble/api (2.0.22)
2025-Jul-27 02:21:28.032340
#13 0.550   - Downloading botble/assets (1.0.29)
2025-Jul-27 02:21:28.032340
#13 0.551   - Downloading openspout/openspout (v4.28.5)
2025-Jul-27 02:21:28.032340
#13 0.551   - Downloading spatie/simple-excel (3.7.2)
2025-Jul-27 02:21:28.032340
#13 0.552   - Downloading markbaker/matrix (3.0.1)
2025-Jul-27 02:21:28.032340
#13 0.553   - Downloading markbaker/complex (3.0.2)
2025-Jul-27 02:21:28.032340
#13 0.553   - Downloading maennchen/zipstream-php (3.1.2)
2025-Jul-27 02:21:28.032340
#13 0.553   - Downloading ezyang/htmlpurifier (v4.18.0)
2025-Jul-27 02:21:28.032340
#13 0.553   - Downloading composer/pcre (3.3.2)
2025-Jul-27 02:21:28.032340
#13 0.554   - Downloading phpoffice/phpspreadsheet (1.29.10)
2025-Jul-27 02:21:28.032340
#13 0.554   - Downloading composer/semver (3.4.3)
2025-Jul-27 02:21:28.032340
#13 0.555   - Downloading maatwebsite/excel (3.1.64)
2025-Jul-27 02:21:28.032340
#13 0.556   - Downloading yajra/laravel-datatables-oracle (v12.1.0)
2025-Jul-27 02:21:28.032340
#13 0.558   - Downloading yajra/laravel-datatables-html (v12.0.2)
2025-Jul-27 02:21:28.032340
#13 0.558   - Downloading yajra/laravel-datatables-buttons (v12.1.3)
2025-Jul-27 02:21:28.032340
#13 0.558   - Downloading twig/twig (v3.20.0)
2025-Jul-27 02:21:28.032340
#13 0.558   - Downloading tightenco/ziggy (v2.5.2)
2025-Jul-27 02:21:28.032340
#13 0.558   - Downloading symfony/postmark-mailer (v7.2.4)
2025-Jul-27 02:21:28.032340
#13 0.558   - Downloading symfony/mailgun-mailer (v7.2.0)
2025-Jul-27 02:21:28.032340
#13 0.559   - Downloading symfony/http-client-contracts (v3.5.2)
2025-Jul-27 02:21:28.032340
#13 0.559   - Downloading symfony/http-client (v7.2.4)
2025-Jul-27 02:21:28.032340
#13 0.560   - Downloading setasign/fpdi (v2.6.3)
2025-Jul-27 02:21:28.032340
#13 0.560   - Downloading paragonie/random_compat (v9.99.100)
2025-Jul-27 02:21:28.032340
#13 0.561   - Downloading myclabs/deep-copy (1.13.1)
2025-Jul-27 02:21:28.032340
#13 0.562   - Downloading mpdf/psr-log-aware-trait (v3.0.0)
2025-Jul-27 02:21:28.032340
#13 0.562   - Downloading mpdf/psr-http-message-shim (v2.0.1)
2025-Jul-27 02:21:28.032340
#13 0.562   - Downloading mpdf/mpdf (v8.2.5)
2025-Jul-27 02:21:28.032340
#13 0.563   - Downloading psr/cache (3.0.0)
2025-Jul-27 02:21:28.032340
#13 0.563   - Downloading mobiledetect/mobiledetectlib (4.8.09)
2025-Jul-27 02:21:28.032340
#13 0.564   - Downloading mews/purifier (3.4.3)
2025-Jul-27 02:21:28.032340
#13 0.564   - Downloading mtdowling/jmespath.php (2.8.0)
2025-Jul-27 02:21:28.032340
#13 0.565   - Downloading aws/aws-sdk-php (3.343.2)
2025-Jul-27 02:21:28.032340
#13 0.565   - Downloading league/flysystem-aws-s3-v3 (3.29.0)
2025-Jul-27 02:21:28.032340
#13 0.566   - Downloading intervention/gif (4.2.2)
2025-Jul-27 02:21:28.032340
#13 0.567   - Downloading intervention/image (3.11.2)
2025-Jul-27 02:21:28.032340
#13 0.567   - Downloading botble/form-builder (1.0.12)
2025-Jul-27 02:21:28.032340
#13 0.567   - Downloading botble/data-synchronize (1.0.31)
2025-Jul-27 02:21:28.032340
#13 0.568   - Downloading botble/dev-tool (1.0.11)
2025-Jul-27 02:21:28.032340
#13 0.568   - Downloading laravel/pint (v1.22.0)
2025-Jul-27 02:21:28.032340
#13 0.570   - Downloading botble/git-commit-checker (2.1.9)
2025-Jul-27 02:21:28.032340
#13 0.570   - Downloading deployer/deployer (v7.5.12)
2025-Jul-27 02:21:28.032340
#13 0.571   - Downloading doctrine/deprecations (1.1.5)
2025-Jul-27 02:21:28.032340
#13 0.571   - Downloading doctrine/dbal (4.2.3)
2025-Jul-27 02:21:28.032340
#13 0.572   - Downloading phpstan/phpstan (2.1.13)
2025-Jul-27 02:21:28.032340
#13 0.572   - Downloading rector/rector (2.0.14)
2025-Jul-27 02:21:28.032340
#13 0.574   - Downloading driftingly/rector-laravel (2.0.4)
2025-Jul-27 02:21:28.032340
#13 0.574   - Downloading fakerphp/faker (v1.24.1)
2025-Jul-27 02:21:28.032340
#13 0.577   - Downloading grpc/grpc (1.57.0)
2025-Jul-27 02:21:28.032340
#13 0.577   - Downloading google/protobuf (v4.30.2)
2025-Jul-27 02:21:28.032340
#13 0.577   - Downloading google/longrunning (0.4.7)
2025-Jul-27 02:21:28.032340
#13 0.577   - Downloading firebase/php-jwt (v6.11.1)
2025-Jul-27 02:21:28.032340
#13 0.578   - Downloading google/auth (v1.47.0)
2025-Jul-27 02:21:28.032340
#13 0.579   - Downloading google/grpc-gcp (v0.4.1)
2025-Jul-27 02:21:28.032340
#13 0.580   - Downloading google/common-protos (4.12.0)
2025-Jul-27 02:21:28.032340
#13 0.582   - Downloading google/gax (v1.36.0)
2025-Jul-27 02:21:28.032340
#13 0.582   - Downloading google/analytics-data (v0.22.2)
2025-Jul-27 02:21:28.032340
#13 0.582   - Downloading khaled.alshamaa/ar-php (v7.0.0)
2025-Jul-27 02:21:28.032340
#13 0.582   - Downloading iamcal/sql-parser (v0.6)
2025-Jul-27 02:21:28.032340
#13 0.583   - Downloading larastan/larastan (v3.4.0)
2025-Jul-27 02:21:28.032340
#13 0.583   - Downloading laravel/pail (v1.2.2)
2025-Jul-27 02:21:28.032340
#13 0.584   - Downloading symfony/yaml (v7.2.5)
2025-Jul-27 02:21:28.032340
#13 0.585   - Downloading laravel/sail (v1.42.0)
2025-Jul-27 02:21:28.032340
#13 0.587   - Downloading laravel/sanctum (v4.1.1)
2025-Jul-27 02:21:28.032340
#13 0.587   - Downloading paragonie/constant_time_encoding (v3.0.0)
2025-Jul-27 02:21:28.032340
#13 0.587   - Downloading phpseclib/phpseclib (3.0.43)
2025-Jul-27 02:21:28.032340
#13 0.588   - Downloading league/oauth1-client (v1.11.0)
2025-Jul-27 02:21:28.032340
#13 0.588   - Downloading laravel/socialite (v5.20.0)
2025-Jul-27 02:21:28.032340
#13 0.589   - Downloading nikic/php-parser (v5.4.0)
2025-Jul-27 02:21:28.032340
#13 0.590   - Downloading psy/psysh (v0.12.8)
2025-Jul-27 02:21:28.032340
#13 0.590   - Downloading laravel/tinker (v2.10.1)
2025-Jul-27 02:21:28.032340
#13 0.591   - Downloading hamcrest/hamcrest-php (v2.1.1)
2025-Jul-27 02:21:28.032340
#13 0.594   - Downloading mockery/mockery (1.6.12)
2025-Jul-27 02:21:28.032340
#13 0.594   - Downloading composer/ca-bundle (1.5.6)
2025-Jul-27 02:21:28.032340
#13 0.594   - Downloading mollie/mollie-api-php (v2.79.0)
2025-Jul-27 02:21:28.032340
#13 0.594   - Downloading mollie/laravel-mollie (v3.1.0)
2025-Jul-27 02:21:28.032340
#13 0.595   - Downloading filp/whoops (2.18.0)
2025-Jul-27 02:21:28.032340
#13 0.596   - Downloading nunomaduro/collision (v8.8.0)
2025-Jul-27 02:21:28.032340
#13 0.597   - Downloading paypal/paypalhttp (1.0.1)
2025-Jul-27 02:21:28.032340
#13 0.599   - Downloading paypal/paypal-checkout-sdk (1.0.2)
2025-Jul-27 02:21:28.032340
#13 0.599   - Downloading phpseclib/bcmath_compat (2.0.3)
2025-Jul-27 02:21:28.032340
#13 0.599   - Downloading staabm/side-effects-detector (1.0.5)
2025-Jul-27 02:21:28.032340
#13 0.599   - Downloading sebastian/version (5.0.2)
2025-Jul-27 02:21:28.032340
#13 0.599   - Downloading sebastian/type (5.1.2)
2025-Jul-27 02:21:28.032340
#13 0.601   - Downloading sebastian/recursion-context (6.0.2)
2025-Jul-27 02:21:28.032340
#13 0.601   - Downloading sebastian/object-reflector (4.0.1)
2025-Jul-27 02:21:28.032340
#13 0.601   - Downloading sebastian/object-enumerator (6.0.1)
2025-Jul-27 02:21:28.032340
#13 0.601   - Downloading sebastian/global-state (7.0.2)
2025-Jul-27 02:21:28.032340
#13 0.601   - Downloading sebastian/exporter (6.3.0)
2025-Jul-27 02:21:28.032340
#13 0.602   - Downloading sebastian/environment (7.2.0)
2025-Jul-27 02:21:28.032340
#13 0.603   - Downloading sebastian/diff (6.0.2)
2025-Jul-27 02:21:28.032340
#13 0.603   - Downloading sebastian/comparator (6.3.1)
2025-Jul-27 02:21:28.032340
#13 0.604   - Downloading sebastian/code-unit (3.0.3)
2025-Jul-27 02:21:28.032340
#13 0.607   - Downloading sebastian/cli-parser (3.0.2)
2025-Jul-27 02:21:28.032340
#13 0.607   - Downloading phpunit/php-timer (7.0.1)
2025-Jul-27 02:21:28.032340
#13 0.607   - Downloading phpunit/php-text-template (4.0.1)
2025-Jul-27 02:21:28.032340
#13 0.607   - Downloading phpunit/php-invoker (5.0.1)
2025-Jul-27 02:21:28.032340
#13 0.607   - Downloading phpunit/php-file-iterator (5.1.0)
2025-Jul-27 02:21:28.032340
#13 0.610   - Downloading theseer/tokenizer (1.2.3)
2025-Jul-27 02:21:28.032340
#13 0.610   - Downloading sebastian/lines-of-code (3.0.1)
2025-Jul-27 02:21:28.032340
#13 0.610   - Downloading sebastian/complexity (4.0.1)
2025-Jul-27 02:21:28.032340
#13 0.610   - Downloading sebastian/code-unit-reverse-lookup (4.0.1)
2025-Jul-27 02:21:28.032340
#13 0.610   - Downloading phpunit/php-code-coverage (11.0.9)
2025-Jul-27 02:21:28.032340
#13 0.611   - Downloading phar-io/version (3.2.1)
2025-Jul-27 02:21:28.032340
#13 0.612   - Downloading phar-io/manifest (2.0.4)
2025-Jul-27 02:21:28.032340
#13 0.612   - Downloading phpunit/phpunit (11.5.18)
2025-Jul-27 02:21:28.032340
#13 0.615   - Downloading predis/predis (v2.4.0)
2025-Jul-27 02:21:28.032340
#13 0.615   - Downloading rmccue/requests (v2.0.15)
2025-Jul-27 02:21:28.032340
#13 0.615   - Downloading razorpay/razorpay (2.9.1)
2025-Jul-27 02:21:28.032340
#13 0.615   - Downloading spatie/error-solutions (1.1.3)
2025-Jul-27 02:21:28.032340
#13 0.616   - Downloading spatie/backtrace (1.7.2)
2025-Jul-27 02:21:28.032340
#13 0.618   - Downloading spatie/flare-client-php (1.10.1)
2025-Jul-27 02:21:28.032340
#13 0.618   - Downloading spatie/ignition (1.15.1)
2025-Jul-27 02:21:28.213169
#13 0.619   - Downloading spatie/laravel-ignition (2.9.1)
2025-Jul-27 02:21:28.213169
#13 0.620   - Downloading stripe/stripe-php (v17.2.0)
2025-Jul-27 02:21:28.213169
#13 0.620   - Downloading symfony/var-exporter (v7.2.5)
2025-Jul-27 02:21:28.213169
#13 0.620   - Downloading symfony/cache-contracts (v3.5.1)
2025-Jul-27 02:21:28.213169
#13 0.621   - Downloading symfony/cache (v7.2.5)
2025-Jul-27 02:21:28.213169
#13 0.651    0/199 [>---------------------------]   0%
2025-Jul-27 02:21:28.614743
#13 1.203   24/199 [===>------------------------]  12%
2025-Jul-27 02:21:28.938303
#13 1.527   46/199 [======>---------------------]  23%
2025-Jul-27 02:21:29.143379
#13 1.729   60/199 [========>-------------------]  30%
2025-Jul-27 02:21:29.555887
#13 2.144   82/199 [===========>----------------]  41%
2025-Jul-27 02:21:30.023536
#13 2.612  103/199 [==============>-------------]  51%
2025-Jul-27 02:21:30.628229
#13 3.216  121/199 [=================>----------]  60%
2025-Jul-27 02:21:31.468149
#13 4.056  144/199 [====================>-------]  72%
2025-Jul-27 02:21:31.802979
#13 4.391  160/199 [======================>-----]  80%
2025-Jul-27 02:21:32.145477
#13 4.732  184/199 [=========================>--]  92%
2025-Jul-27 02:21:33.461148
#13 6.048  198/199 [===========================>]  99%
2025-Jul-27 02:21:34.084204
#13 6.673  199/199 [============================] 100%
2025-Jul-27 02:21:34.192010
#13 6.677   - Installing wikimedia/composer-merge-plugin (v2.1.0): Extracting archive
2025-Jul-27 02:21:34.192010
#13 6.732   - Installing aws/aws-crt-php (v1.2.7): Extracting archive
2025-Jul-27 02:21:34.192010
#13 6.737   - Installing symfony/finder (v7.2.2): Extracting archive
2025-Jul-27 02:21:34.192010
#13 6.742   - Installing symfony/polyfill-mbstring (v1.31.0): Extracting archive
2025-Jul-27 02:21:34.192010
#13 6.751   - Installing symfony/var-dumper (v7.2.3): Extracting archive
2025-Jul-27 02:21:34.192010
#13 6.763   - Installing psr/log (3.0.2): Extracting archive
2025-Jul-27 02:21:34.192010
#13 6.779   - Installing php-debugbar/php-debugbar (v2.1.6): Extracting archive
2025-Jul-27 02:21:34.350658
#13 6.831   - Installing voku/portable-ascii (2.0.3): Extracting archive
2025-Jul-27 02:21:34.350658
#13 6.874   - Installing symfony/polyfill-php80 (v1.31.0): Extracting archive
2025-Jul-27 02:21:34.350658
#13 6.935   - Installing symfony/polyfill-ctype (v1.31.0): Extracting archive
2025-Jul-27 02:21:34.449175
#13 6.978   - Installing phpoption/phpoption (1.9.3): Extracting archive
2025-Jul-27 02:21:34.449175
#13 7.012   - Installing graham-campbell/result-type (v1.1.3): Extracting archive
2025-Jul-27 02:21:34.449175
#13 7.030   - Installing vlucas/phpdotenv (v5.6.2): Extracting archive
2025-Jul-27 02:21:34.449175
#13 7.030   - Installing symfony/css-selector (v7.2.0): Extracting archive
2025-Jul-27 02:21:34.449175
#13 7.037   - Installing tijsverkoyen/css-to-inline-styles (v2.3.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.040   - Installing symfony/polyfill-uuid (v1.31.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.051   - Installing symfony/uid (v7.2.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.051   - Installing symfony/deprecation-contracts (v3.5.1): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.051   - Installing symfony/routing (v7.2.3): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.051   - Installing symfony/process (v7.2.5): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.063   - Installing symfony/polyfill-php83 (v1.31.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.066   - Installing symfony/polyfill-intl-normalizer (v1.31.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.068   - Installing symfony/polyfill-intl-idn (v1.31.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.072   - Installing symfony/mime (v7.2.4): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.074   - Installing psr/container (2.0.2): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.084   - Installing symfony/service-contracts (v3.5.1): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.086   - Installing psr/event-dispatcher (1.0.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.093   - Installing symfony/event-dispatcher-contracts (v3.5.1): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.093   - Installing symfony/event-dispatcher (v7.2.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.098   - Installing doctrine/lexer (3.0.1): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.098   - Installing egulias/email-validator (4.0.4): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.098   - Installing symfony/mailer (v7.2.3): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.102   - Installing symfony/http-foundation (v7.2.5): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.105   - Installing symfony/error-handler (v7.2.5): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.107   - Installing symfony/http-kernel (v7.2.5): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.111   - Installing symfony/polyfill-intl-grapheme (v1.31.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.116   - Installing symfony/string (v7.2.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.119   - Installing symfony/console (v7.2.5): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.119   - Installing ramsey/collection (2.1.1): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.121   - Installing brick/math (0.12.3): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.125   - Installing ramsey/uuid (4.7.6): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.129   - Installing psr/simple-cache (3.0.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.129   - Installing nunomaduro/termwind (v2.3.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.132   - Installing symfony/translation-contracts (v3.5.1): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.134   - Installing symfony/translation (v7.2.4): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.135   - Installing psr/clock (1.0.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.139   - Installing symfony/clock (v7.2.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.139   - Installing carbonphp/carbon-doctrine-types (3.2.0): Extracting archive
2025-Jul-27 02:21:34.552187
#13 7.139   - Installing nesbot/carbon (3.9.1): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.141   - Installing monolog/monolog (3.9.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.143   - Installing psr/http-message (2.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.145   - Installing psr/http-factory (1.1.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.148   - Installing league/uri-interfaces (7.5.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.149   - Installing league/uri (7.5.1): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.154   - Installing league/mime-type-detection (1.16.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.154   - Installing league/flysystem-local (3.29.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.154   - Installing league/flysystem (3.29.1): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.154   - Installing nette/utils (v4.0.6): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.160   - Installing nette/schema (v1.3.2): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.160   - Installing dflydev/dot-access-data (v3.0.3): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.160   - Installing league/config (v1.2.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.163   - Installing league/commonmark (2.6.2): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.164   - Installing laravel/serializable-closure (v2.0.4): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.166   - Installing laravel/prompts (v0.3.5): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.167   - Installing guzzlehttp/uri-template (v1.0.4): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.168   - Installing psr/http-client (1.0.3): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.168   - Installing ralouphie/getallheaders (3.0.3): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.169   - Installing guzzlehttp/psr7 (2.7.1): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.170   - Installing guzzlehttp/promises (2.2.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.171   - Installing guzzlehttp/guzzle (7.9.3): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.174   - Installing fruitcake/php-cors (v1.3.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.174   - Installing webmozart/assert (1.11.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.174   - Installing dragonmantank/cron-expression (v3.4.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.175   - Installing doctrine/inflector (2.0.10): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.176   - Installing laravel/framework (v12.12.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.177   - Installing barryvdh/laravel-debugbar (v3.15.4): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.179   - Installing masterminds/html5 (2.9.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.179   - Installing sabberworm/php-css-parser (v8.8.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.180   - Installing dompdf/php-svg-lib (1.0.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.184   - Installing dompdf/php-font-lib (1.0.1): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.184   - Installing dompdf/dompdf (v3.1.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.184   - Installing barryvdh/laravel-dompdf (v3.1.1): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.184   - Installing botble/api (2.0.22): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.186   - Installing botble/assets (1.0.29): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.187   - Installing openspout/openspout (v4.28.5): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.188   - Installing spatie/simple-excel (3.7.2): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.190   - Installing markbaker/matrix (3.0.1): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.192   - Installing markbaker/complex (3.0.2): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.192   - Installing maennchen/zipstream-php (3.1.2): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.193   - Installing ezyang/htmlpurifier (v4.18.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.194   - Installing composer/pcre (3.3.2): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.195   - Installing phpoffice/phpspreadsheet (1.29.10): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.196   - Installing composer/semver (3.4.3): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.196   - Installing maatwebsite/excel (3.1.64): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.197   - Installing yajra/laravel-datatables-oracle (v12.1.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.198   - Installing yajra/laravel-datatables-html (v12.0.2): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.200   - Installing yajra/laravel-datatables-buttons (v12.1.3): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.201   - Installing twig/twig (v3.20.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.203   - Installing tightenco/ziggy (v2.5.2): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.203   - Installing symfony/postmark-mailer (v7.2.4): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.206   - Installing symfony/mailgun-mailer (v7.2.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.206   - Installing symfony/http-client-contracts (v3.5.2): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.208   - Installing symfony/http-client (v7.2.4): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.208   - Installing setasign/fpdi (v2.6.3): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.208   - Installing paragonie/random_compat (v9.99.100): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.209   - Installing myclabs/deep-copy (1.13.1): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.211   - Installing mpdf/psr-log-aware-trait (v3.0.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.212   - Installing mpdf/psr-http-message-shim (v2.0.1): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.215   - Installing mpdf/mpdf (v8.2.5): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.215   - Installing psr/cache (3.0.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.215   - Installing mobiledetect/mobiledetectlib (4.8.09): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.215   - Installing mews/purifier (3.4.3): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.218   - Installing mtdowling/jmespath.php (2.8.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.218   - Installing aws/aws-sdk-php (3.343.2): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.219   - Installing league/flysystem-aws-s3-v3 (3.29.0): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.221   - Installing intervention/gif (4.2.2): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.222   - Installing intervention/image (3.11.2): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.223   - Installing botble/form-builder (1.0.12): Extracting archive
2025-Jul-27 02:21:34.655774
#13 7.230   - Installing botble/platform (dev-features/rentals): Symlinking from ./platform/core
2025-Jul-27 02:21:34.655774
#13 7.244   - Installing botble/data-synchronize (1.0.31): Extracting archive
2025-Jul-27 02:21:34.756004
#13 7.245   - Installing botble/dev-tool (1.0.11): Extracting archive
2025-Jul-27 02:21:34.756004
#13 7.250   - Installing botble/get-started (dev-features/rentals): Symlinking from ./platform/packages/get-started
2025-Jul-27 02:21:34.756004
#13 7.260   - Installing laravel/pint (v1.22.0): Extracting archive
2025-Jul-27 02:21:34.756004
#13 7.260   - Installing botble/git-commit-checker (2.1.9): Extracting archive
2025-Jul-27 02:21:34.756004
#13 7.263   - Installing botble/installer (dev-features/rentals): Symlinking from ./platform/packages/installer
2025-Jul-27 02:21:34.756004
#13 7.268   - Installing botble/theme (dev-features/rentals): Symlinking from ./platform/packages/theme
2025-Jul-27 02:21:34.756004
#13 7.275   - Installing botble/widget (dev-features/rentals): Symlinking from ./platform/packages/widget
2025-Jul-27 02:21:34.756004
#13 7.279   - Installing botble/slug (dev-features/rentals): Symlinking from ./platform/packages/slug
2025-Jul-27 02:21:34.777503
#13 7.284   - Installing botble/sitemap (dev-features/rentals): Symlinking from ./platform/packages/sitemap
2025-Jul-27 02:21:34.777503
#13 7.290   - Installing botble/seo-helper (dev-features/rentals): Symlinking from ./platform/packages/seo-helper
2025-Jul-27 02:21:34.777503
#13 7.294   - Installing botble/menu (dev-features/rentals): Symlinking from ./platform/packages/menu
2025-Jul-27 02:21:34.777503
#13 7.298   - Installing botble/optimize (dev-features/rentals): Symlinking from ./platform/packages/optimize
2025-Jul-27 02:21:34.777503
#13 7.302   - Installing botble/revision (dev-features/rentals): Symlinking from ./platform/packages/revision
2025-Jul-27 02:21:34.777503
#13 7.317   - Installing botble/page (dev-features/rentals): Symlinking from ./platform/packages/page
2025-Jul-27 02:21:34.777503
#13 7.321   - Installing botble/plugin-management (dev-features/rentals): Symlinking from ./platform/packages/plugin-management
2025-Jul-27 02:21:34.777503
#13 7.325   - Installing botble/shortcode (dev-features/rentals): Symlinking from ./platform/packages/shortcode
2025-Jul-27 02:21:34.777503
#13 7.329   - Installing deployer/deployer (v7.5.12): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.330   - Installing doctrine/deprecations (1.1.5): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.331   - Installing doctrine/dbal (4.2.3): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.332   - Installing phpstan/phpstan (2.1.13): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.332   - Installing rector/rector (2.0.14): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.333   - Installing driftingly/rector-laravel (2.0.4): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.333   - Installing fakerphp/faker (v1.24.1): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.334   - Installing grpc/grpc (1.57.0): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.334   - Installing google/protobuf (v4.30.2): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.335   - Installing google/longrunning (0.4.7): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.335   - Installing firebase/php-jwt (v6.11.1): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.336   - Installing google/auth (v1.47.0): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.336   - Installing google/grpc-gcp (v0.4.1): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.337   - Installing google/common-protos (4.12.0): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.337   - Installing google/gax (v1.36.0): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.338   - Installing google/analytics-data (v0.22.2): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.339   - Installing khaled.alshamaa/ar-php (v7.0.0): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.340   - Installing iamcal/sql-parser (v0.6): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.340   - Installing larastan/larastan (v3.4.0): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.340   - Installing laravel/pail (v1.2.2): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.340   - Installing symfony/yaml (v7.2.5): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.341   - Installing laravel/sail (v1.42.0): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.342   - Installing laravel/sanctum (v4.1.1): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.343   - Installing paragonie/constant_time_encoding (v3.0.0): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.343   - Installing phpseclib/phpseclib (3.0.43): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.343   - Installing league/oauth1-client (v1.11.0): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.344   - Installing laravel/socialite (v5.20.0): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.344   - Installing nikic/php-parser (v5.4.0): Extracting archive
2025-Jul-27 02:21:34.777503
#13 7.345   - Installing psy/psysh (v0.12.8): Extracting archive
2025-Jul-27 02:21:34.864697
#13 7.347   - Installing laravel/tinker (v2.10.1): Extracting archive
2025-Jul-27 02:21:34.864697
#13 7.348   - Installing hamcrest/hamcrest-php (v2.1.1): Extracting archive
2025-Jul-27 02:21:34.864697
#13 7.349   - Installing mockery/mockery (1.6.12): Extracting archive
2025-Jul-27 02:21:34.864697
#13 7.352   - Installing composer/ca-bundle (1.5.6): Extracting archive
2025-Jul-27 02:21:34.864697
#13 7.352   - Installing mollie/mollie-api-php (v2.79.0): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.352   - Installing mollie/laravel-mollie (v3.1.0): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.352   - Installing filp/whoops (2.18.0): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.353   - Installing nunomaduro/collision (v8.8.0): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.356   - Installing paypal/paypalhttp (1.0.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.356   - Installing paypal/paypal-checkout-sdk (1.0.2): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.356   - Installing phpseclib/bcmath_compat (2.0.3): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.357   - Installing staabm/side-effects-detector (1.0.5): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.357   - Installing sebastian/version (5.0.2): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.358   - Installing sebastian/type (5.1.2): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.362   - Installing sebastian/recursion-context (6.0.2): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.362   - Installing sebastian/object-reflector (4.0.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.362   - Installing sebastian/object-enumerator (6.0.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.362   - Installing sebastian/global-state (7.0.2): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.363   - Installing sebastian/exporter (6.3.0): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.363   - Installing sebastian/environment (7.2.0): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.364   - Installing sebastian/diff (6.0.2): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.365   - Installing sebastian/comparator (6.3.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.366   - Installing sebastian/code-unit (3.0.3): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.367   - Installing sebastian/cli-parser (3.0.2): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.369   - Installing phpunit/php-timer (7.0.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.369   - Installing phpunit/php-text-template (4.0.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.369   - Installing phpunit/php-invoker (5.0.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.369   - Installing phpunit/php-file-iterator (5.1.0): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.370   - Installing theseer/tokenizer (1.2.3): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.370   - Installing sebastian/lines-of-code (3.0.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.371   - Installing sebastian/complexity (4.0.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.372   - Installing sebastian/code-unit-reverse-lookup (4.0.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.373   - Installing phpunit/php-code-coverage (11.0.9): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.373   - Installing phar-io/version (3.2.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.376   - Installing phar-io/manifest (2.0.4): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.376   - Installing phpunit/phpunit (11.5.18): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.376   - Installing predis/predis (v2.4.0): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.377   - Installing rmccue/requests (v2.0.15): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.379   - Installing razorpay/razorpay (2.9.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.379   - Installing spatie/error-solutions (1.1.3): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.379   - Installing spatie/backtrace (1.7.2): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.382   - Installing spatie/flare-client-php (1.10.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.382   - Installing spatie/ignition (1.15.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.382   - Installing spatie/laravel-ignition (2.9.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.382   - Installing stripe/stripe-php (v17.2.0): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.383   - Installing symfony/var-exporter (v7.2.5): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.384   - Installing symfony/cache-contracts (v3.5.1): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.385   - Installing symfony/cache (v7.2.5): Extracting archive
2025-Jul-27 02:21:34.882668
#13 7.453    0/198 [>---------------------------]   0%
2025-Jul-27 02:21:35.283314
#13 7.871   20/198 [==>-------------------------]  10%
2025-Jul-27 02:21:36.069041
#13 8.655   40/198 [=====>----------------------]  20%
2025-Jul-27 02:21:37.057339
2025-Jul-27 02:21:37.078208
#13 9.644   64/198 [=========>------------------]  32%
2025-Jul-27 02:21:38.246498
#13 10.83   78/198 [===========>----------------]  39%
2025-Jul-27 02:21:38.580501
#13 11.17   81/198 [===========>----------------]  40%
2025-Jul-27 02:21:39.858509
#13 12.45  101/198 [==============>-------------]  51%
2025-Jul-27 02:21:41.067453
#13 13.66  117/198 [================>-----------]  59%
2025-Jul-27 02:21:41.448919
#13 14.04  119/198 [================>-----------]  60%
2025-Jul-27 02:21:42.477442
#13 15.07  125/198 [=================>----------]  63%
2025-Jul-27 02:21:43.658208
#13 16.25  133/198 [==================>---------]  67%
2025-Jul-27 02:21:44.460311
#13 17.05  139/198 [===================>--------]  70%
2025-Jul-27 02:21:45.476004
2025-Jul-27 02:21:45.493162
#13 18.06  144/198 [====================>-------]  72%
2025-Jul-27 02:21:46.740095
#13 19.33  155/198 [=====================>------]  78%
2025-Jul-27 02:21:47.135719
2025-Jul-27 02:21:47.162945
#13 19.72  161/198 [======================>-----]  81%
2025-Jul-27 02:21:47.795924
#13 20.38  181/198 [=========================>--]  91%
2025-Jul-27 02:21:48.513104
#13 21.10  198/198 [============================] 100%
2025-Jul-27 02:21:48.662836
2025-Jul-27 02:21:48.729140
#13 21.32 Package paypal/paypal-checkout-sdk is abandoned, you should avoid using it. Use paypal/paypal-server-sdk instead.
2025-Jul-27 02:21:48.729140
#13 21.32 Generating optimized autoload files
2025-Jul-27 02:21:52.994028
#13 25.58 > Illuminate\Foundation\ComposerScripts::postAutoloadDump
2025-Jul-27 02:21:53.185511
#13 25.62 > @php artisan package:discover --ansi
2025-Jul-27 02:21:53.624165
#13 26.21
2025-Jul-27 02:21:53.624165
#13 26.21    INFO  Discovering packages.
2025-Jul-27 02:21:53.624165
#13 26.21
2025-Jul-27 02:21:53.737279
#13 26.23   barryvdh/laravel-debugbar ............................................. DONE
2025-Jul-27 02:21:53.737279
#13 26.23   barryvdh/laravel-dompdf ............................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.23   botble/api ............................................................ DONE
2025-Jul-27 02:21:53.737279
#13 26.23   botble/assets ......................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.23   botble/data-synchronize ............................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.23   botble/dev-tool ....................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.23   botble/form-builder ................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.24   botble/get-started .................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.24   botble/git-commit-checker ............................................. DONE
2025-Jul-27 02:21:53.737279
#13 26.24   botble/installer ...................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.24   botble/menu ........................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.24   botble/optimize ....................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.24   botble/page ........................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.25   botble/platform ....................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.25   botble/plugin-management .............................................. DONE
2025-Jul-27 02:21:53.737279
#13 26.25   botble/revision ....................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.25   botble/seo-helper ..................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.25   botble/shortcode ...................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.25   botble/sitemap ........................................................ DONE
2025-Jul-27 02:21:53.737279
#13 26.25   botble/slug ........................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.26   botble/theme .......................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.26   botble/widget ......................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.26   laravel/pail .......................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.26   laravel/sail .......................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.26   laravel/sanctum ....................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.26   laravel/socialite ..................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.26   laravel/tinker ........................................................ DONE
2025-Jul-27 02:21:53.737279
#13 26.26   maatwebsite/excel ..................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.26   mews/purifier ......................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.27   mollie/laravel-mollie ................................................. DONE
2025-Jul-27 02:21:53.737279
#13 26.27   nesbot/carbon ......................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.27   nunomaduro/collision .................................................. DONE
2025-Jul-27 02:21:53.737279
#13 26.27   nunomaduro/termwind ................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.27   spatie/laravel-ignition ............................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.27   tightenco/ziggy ....................................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.27   yajra/laravel-datatables-buttons ...................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.27   yajra/laravel-datatables-html ......................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.27   yajra/laravel-datatables-oracle ....................................... DONE
2025-Jul-27 02:21:53.737279
#13 26.28
2025-Jul-27 02:21:53.737279
#13 26.28    INFO  Discovering plugins.
2025-Jul-27 02:21:53.737279
#13 26.28
2025-Jul-27 02:21:53.737279
#13 26.32 118 packages you are using are looking for funding.
2025-Jul-27 02:21:53.840807
#13 26.32 Use the `composer fund` command to find out more!
2025-Jul-27 02:21:53.840807
#13 26.32
2025-Jul-27 02:21:53.840807
#13 26.32 Running composer update to apply merge settings
2025-Jul-27 02:21:53.840807
#13 26.43 Loading composer repositories with package information
2025-Jul-27 02:21:54.513923
#13 27.10 Updating dependencies
2025-Jul-27 02:21:54.528209
2025-Jul-27 02:21:54.729583
#13 27.14 Lock file operations: 0 installs, 20 updates, 0 removals
2025-Jul-27 02:21:54.729583
#13 27.14   - Upgrading botble/get-started (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading botble/installer (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading botble/menu (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading botble/optimize (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading botble/page (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading botble/platform (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading botble/plugin-management (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading botble/revision (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading botble/seo-helper (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading botble/shortcode (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading botble/sitemap (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading botble/slug (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading botble/theme (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading botble/widget (dev-features/rentals => dev-main)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading google/analytics-data (v0.22.2 => v0.22.3)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading laravel/socialite (v5.20.0 => v5.23.0)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading spatie/simple-excel (3.7.2 => 3.7.3)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading stripe/stripe-php (v17.2.0 => v17.4.0)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading symfony/cache (v7.2.5 => v7.2.8)
2025-Jul-27 02:21:54.740034
#13 27.14   - Upgrading symfony/var-exporter (v7.2.5 => v7.3.0)
2025-Jul-27 02:21:54.740034
#13 27.15 Writing lock file
2025-Jul-27 02:21:54.740034
#13 27.15 Installing dependencies from lock file (including require-dev)
2025-Jul-27 02:21:54.740034
#13 27.16 Package operations: 0 installs, 20 updates, 0 removals
2025-Jul-27 02:21:54.740034
#13 27.16   - Downloading spatie/simple-excel (3.7.3)
2025-Jul-27 02:21:54.740034
#13 27.16   - Downloading google/analytics-data (v0.22.3)
2025-Jul-27 02:21:54.740034
#13 27.17   - Downloading laravel/socialite (v5.23.0)
2025-Jul-27 02:21:54.740034
#13 27.17   - Downloading stripe/stripe-php (v17.4.0)
2025-Jul-27 02:21:54.740034
#13 27.17   - Downloading symfony/var-exporter (v7.3.0)
2025-Jul-27 02:21:54.740034
#13 27.17   - Downloading symfony/cache (v7.2.8)
2025-Jul-27 02:21:55.028688
#13 27.62   - Upgrading spatie/simple-excel (3.7.2 => 3.7.3): Extracting archive
2025-Jul-27 02:21:55.132077
#13 27.63   - Upgrading botble/platform (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.132077
#13 27.65   - Upgrading botble/get-started (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.132077
#13 27.66   - Upgrading botble/installer (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.132077
#13 27.67   - Upgrading botble/theme (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.132077
#13 27.68   - Upgrading botble/widget (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.132077
#13 27.69   - Upgrading botble/slug (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.132077
#13 27.70   - Upgrading botble/sitemap (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.132077
#13 27.72   - Upgrading botble/seo-helper (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.262230
#13 27.75   - Upgrading botble/menu (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.262230
#13 27.77   - Upgrading botble/optimize (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.262230
#13 27.79   - Upgrading botble/revision (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.262230
#13 27.82   - Upgrading botble/page (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.262230
#13 27.85   - Upgrading botble/plugin-management (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.368305
#13 27.90   - Upgrading botble/shortcode (dev-features/rentals => dev-main): Source already present
2025-Jul-27 02:21:55.368305
#13 27.93   - Upgrading google/analytics-data (v0.22.2 => v0.22.3): Extracting archive
2025-Jul-27 02:21:55.368305
#13 27.94   - Upgrading laravel/socialite (v5.20.0 => v5.23.0): Extracting archive
2025-Jul-27 02:21:55.368305
#13 27.96   - Upgrading stripe/stripe-php (v17.2.0 => v17.4.0): Extracting archive
2025-Jul-27 02:21:55.574106
#13 27.99   - Upgrading symfony/var-exporter (v7.2.5 => v7.3.0): Extracting archive
2025-Jul-27 02:21:55.574106
#13 28.01   - Upgrading symfony/cache (v7.2.5 => v7.2.8): Extracting archive
2025-Jul-27 02:21:56.047171
#13 28.63 Package paypal/paypal-checkout-sdk is abandoned, you should avoid using it. Use paypal/paypal-server-sdk instead.
2025-Jul-27 02:21:56.047171
#13 28.63 Generating optimized autoload files
2025-Jul-27 02:21:59.960026
#13 32.54 > Illuminate\Foundation\ComposerScripts::postAutoloadDump
2025-Jul-27 02:22:00.118013
#13 32.55 > @php artisan package:discover --ansi
2025-Jul-27 02:22:00.709989
#13 33.30
2025-Jul-27 02:22:00.709989
#13 33.30    INFO  Discovering packages.
2025-Jul-27 02:22:00.709989
#13 33.30
2025-Jul-27 02:22:00.851898
#13 33.33   barryvdh/laravel-debugbar ............................................. DONE
2025-Jul-27 02:22:00.851898
#13 33.33   barryvdh/laravel-dompdf ............................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.33   botble/api ............................................................ DONE
2025-Jul-27 02:22:00.851898
#13 33.33   botble/assets ......................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.33   botble/data-synchronize ............................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.33   botble/dev-tool ....................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.33   botble/form-builder ................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.34   botble/get-started .................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.34   botble/git-commit-checker ............................................. DONE
2025-Jul-27 02:22:00.851898
#13 33.34   botble/installer ...................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.34   botble/menu ........................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.34   botble/optimize ....................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.34   botble/page ........................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.35   botble/platform ....................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.35   botble/plugin-management .............................................. DONE
2025-Jul-27 02:22:00.851898
#13 33.35   botble/revision ....................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.35   botble/seo-helper ..................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.35   botble/shortcode ...................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.35   botble/sitemap ........................................................ DONE
2025-Jul-27 02:22:00.851898
#13 33.35   botble/slug ........................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.36   botble/theme .......................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.36   botble/widget ......................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.36   laravel/pail .......................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.36   laravel/sail .......................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.37   laravel/sanctum ....................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.37   laravel/socialite ..................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.37   laravel/tinker ........................................................ DONE
2025-Jul-27 02:22:00.851898
#13 33.37   maatwebsite/excel ..................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.37   mews/purifier ......................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.37   mollie/laravel-mollie ................................................. DONE
2025-Jul-27 02:22:00.851898
#13 33.37   nesbot/carbon ......................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.38   nunomaduro/collision .................................................. DONE
2025-Jul-27 02:22:00.851898
#13 33.38   nunomaduro/termwind ................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.38   spatie/laravel-ignition ............................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.38   tightenco/ziggy ....................................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.38   yajra/laravel-datatables-buttons ...................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.38   yajra/laravel-datatables-html ......................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.38   yajra/laravel-datatables-oracle ....................................... DONE
2025-Jul-27 02:22:00.851898
#13 33.38
2025-Jul-27 02:22:00.851898
#13 33.39    INFO  Discovering plugins.
2025-Jul-27 02:22:00.851898
#13 33.39
2025-Jul-27 02:22:00.851898
#13 33.44 118 packages you are using are looking for funding.
2025-Jul-27 02:22:01.004111
#13 33.44 Use the `composer fund` command to find out more!
2025-Jul-27 02:22:01.318076
#13 33.90 > @php artisan vendor:publish --tag=laravel-assets --ansi --force
2025-Jul-27 02:22:02.049493
#13 34.64
2025-Jul-27 02:22:02.049493
#13 34.64    INFO  No publishable resources for tag [laravel-assets].
2025-Jul-27 02:22:02.049493
#13 34.64
2025-Jul-27 02:22:02.237108
#13 34.67 > Aws\Script\Composer\Composer::removeUnusedServices
2025-Jul-27 02:22:02.848565
#13 35.44 Removed 397 AWS services
2025-Jul-27 02:22:03.002980
#13 35.44 > @php artisan cms:publish:assets
2025-Jul-27 02:22:03.437721
#13 36.03
2025-Jul-27 02:22:03.437721
#13 36.03    INFO  Publishing core, packages, plugins assets...
2025-Jul-27 02:22:03.437721
#13 36.03
2025-Jul-27 02:22:03.646510
#13 36.03    INFO  Publishing [cms-public] assets.
2025-Jul-27 02:22:03.646510
#13 36.03
2025-Jul-27 02:22:03.646510
#13 36.05   Copying directory [vendor/botble/data-synchronize/public] to [public/vendor/core/packages/data-synchronize]  DONE
2025-Jul-27 02:22:03.646510
#13 36.06   Copying directory [platform/packages/get-started/public] to [public/vendor/core/packages/get-started]  DONE
2025-Jul-27 02:22:03.646510
#13 36.07   Copying directory [platform/packages/installer/public] to [public/vendor/core/packages/installer]  DONE
2025-Jul-27 02:22:03.646510
#13 36.07   Copying directory [platform/packages/menu/public] to [public/vendor/core/packages/menu]  DONE
2025-Jul-27 02:22:03.646510
#13 36.08   Copying directory [platform/core/setting/public] to [public/vendor/core/core/setting]  DONE
2025-Jul-27 02:22:04.386364
#13 36.97   Copying directory [platform/core/base/public] to [public/vendor/core/core/base]  DONE
2025-Jul-27 02:22:04.491384
#13 36.98   Copying directory [platform/core/table/public] to [public/vendor/core/core/table]  DONE
2025-Jul-27 02:22:04.502665
#13 37.03   Copying directory [platform/core/acl/public] to [public/vendor/core/core/acl]  DONE
2025-Jul-27 02:22:04.502665
#13 37.03   Copying directory [platform/core/dashboard/public] to [public/vendor/core/core/dashboard]  DONE
2025-Jul-27 02:22:04.502665
#13 37.05   Copying directory [platform/core/media/public] to [public/vendor/core/core/media]  DONE
2025-Jul-27 02:22:04.502665
#13 37.06   Copying directory [platform/core/js-validation/public] to [public/vendor/core/core/js-validation]  DONE
2025-Jul-27 02:22:04.502665
#13 37.06   Copying directory [platform/packages/plugin-management/public] to [public/vendor/core/packages/plugin-management]  DONE
2025-Jul-27 02:22:04.502665
#13 37.07   Copying directory [platform/packages/revision/public] to [public/vendor/core/packages/revision]  DONE
2025-Jul-27 02:22:04.502665
#13 37.07   Copying directory [platform/packages/seo-helper/public] to [public/vendor/core/packages/seo-helper]  DONE
2025-Jul-27 02:22:04.502665
#13 37.08   Copying directory [platform/packages/shortcode/public] to [public/vendor/core/packages/shortcode]  DONE
2025-Jul-27 02:22:04.661279
#13 37.09   Copying directory [platform/packages/sitemap/public] to [public/vendor/core/packages/sitemap]  DONE
2025-Jul-27 02:22:04.661279
#13 37.10   Copying directory [platform/packages/slug/public] to [public/vendor/core/packages/slug]  DONE
2025-Jul-27 02:22:04.661279
#13 37.11   Copying directory [platform/packages/theme/public] to [public/vendor/core/packages/theme]  DONE
2025-Jul-27 02:22:04.661279
#13 37.12   Copying directory [platform/packages/widget/public] to [public/vendor/core/packages/widget]  DONE
2025-Jul-27 02:22:04.661279
#13 37.12
2025-Jul-27 02:22:04.661279
#13 37.12    INFO  Publishing theme assets...
2025-Jul-27 02:22:04.661279
#13 37.12
2025-Jul-27 02:22:04.661279
#13 37.25    INFO  Publish assets for homzen successfully!
2025-Jul-27 02:22:04.661279
#13 37.25
2025-Jul-27 02:22:04.814544
#13 37.25    INFO  Published assets successfully!
2025-Jul-27 02:22:04.826147
#13 37.25
2025-Jul-27 02:22:05.068991
#13 37.66 Found 1 security vulnerability advisory affecting 1 package.
2025-Jul-27 02:22:05.068991
#13 37.66 Run "composer audit" for a full list of advisories.
2025-Jul-27 02:22:05.326922
#13 DONE 37.9s
2025-Jul-27 02:22:05.484373
#14 [10/13] RUN  npm ci
2025-Jul-27 02:22:07.099655
#14 1.768 npm warn EBADENGINE Unsupported engine {
2025-Jul-27 02:22:07.099655
#14 1.768 npm warn EBADENGINE   package: '@isaacs/balanced-match@4.0.1',
2025-Jul-27 02:22:07.099655
#14 1.768 npm warn EBADENGINE   required: { node: '20 || >=22' },
2025-Jul-27 02:22:07.099655
#14 1.768 npm warn EBADENGINE   current: { node: 'v18.20.5', npm: '10.8.2' }
2025-Jul-27 02:22:07.099655
#14 1.768 npm warn EBADENGINE }
2025-Jul-27 02:22:07.265394
#14 1.772 npm warn EBADENGINE Unsupported engine {
2025-Jul-27 02:22:07.265394
#14 1.772 npm warn EBADENGINE   package: '@isaacs/brace-expansion@5.0.0',
2025-Jul-27 02:22:07.265394
#14 1.772 npm warn EBADENGINE   required: { node: '20 || >=22' },
2025-Jul-27 02:22:07.265394
#14 1.772 npm warn EBADENGINE   current: { node: 'v18.20.5', npm: '10.8.2' }
2025-Jul-27 02:22:07.265394
#14 1.772 npm warn EBADENGINE }
2025-Jul-27 02:22:07.265394
#14 1.777 npm warn EBADENGINE Unsupported engine {
2025-Jul-27 02:22:07.265394
#14 1.777 npm warn EBADENGINE   package: 'glob@11.0.3',
2025-Jul-27 02:22:07.265394
#14 1.777 npm warn EBADENGINE   required: { node: '20 || >=22' },
2025-Jul-27 02:22:07.265394
#14 1.777 npm warn EBADENGINE   current: { node: 'v18.20.5', npm: '10.8.2' }
2025-Jul-27 02:22:07.265394
#14 1.777 npm warn EBADENGINE }
2025-Jul-27 02:22:07.265394
#14 1.781 npm warn EBADENGINE Unsupported engine {
2025-Jul-27 02:22:07.265394
#14 1.781 npm warn EBADENGINE   package: 'jackspeak@4.1.1',
2025-Jul-27 02:22:07.265394
#14 1.781 npm warn EBADENGINE   required: { node: '20 || >=22' },
2025-Jul-27 02:22:07.265394
#14 1.781 npm warn EBADENGINE   current: { node: 'v18.20.5', npm: '10.8.2' }
2025-Jul-27 02:22:07.265394
#14 1.781 npm warn EBADENGINE }
2025-Jul-27 02:22:07.265394
#14 1.781 npm warn EBADENGINE Unsupported engine {
2025-Jul-27 02:22:07.265394
#14 1.781 npm warn EBADENGINE   package: 'minimatch@10.0.3',
2025-Jul-27 02:22:07.265394
#14 1.781 npm warn EBADENGINE   required: { node: '20 || >=22' },
2025-Jul-27 02:22:07.265394
#14 1.781 npm warn EBADENGINE   current: { node: 'v18.20.5', npm: '10.8.2' }
2025-Jul-27 02:22:07.265394
#14 1.781 npm warn EBADENGINE }
2025-Jul-27 02:22:07.265394
#14 1.782 npm warn EBADENGINE Unsupported engine {
2025-Jul-27 02:22:07.265394
#14 1.782 npm warn EBADENGINE   package: 'path-scurry@2.0.0',
2025-Jul-27 02:22:07.265394
#14 1.782 npm warn EBADENGINE   required: { node: '20 || >=22' },
2025-Jul-27 02:22:07.265394
#14 1.782 npm warn EBADENGINE   current: { node: 'v18.20.5', npm: '10.8.2' }
2025-Jul-27 02:22:07.265394
#14 1.782 npm warn EBADENGINE }
2025-Jul-27 02:22:07.265394
#14 1.784 npm warn EBADENGINE Unsupported engine {
2025-Jul-27 02:22:07.265394
#14 1.784 npm warn EBADENGINE   package: 'lru-cache@11.1.0',
2025-Jul-27 02:22:07.265394
#14 1.784 npm warn EBADENGINE   required: { node: '20 || >=22' },
2025-Jul-27 02:22:07.265394
#14 1.784 npm warn EBADENGINE   current: { node: 'v18.20.5', npm: '10.8.2' }
2025-Jul-27 02:22:07.265394
#14 1.784 npm warn EBADENGINE }
2025-Jul-27 02:22:12.185708
#14 6.854 npm warn deprecated stable@0.1.8: Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility
2025-Jul-27 02:22:12.347260
#14 7.016 npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
2025-Jul-27 02:22:14.637059
#14 9.306 npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
2025-Jul-27 02:22:20.471665
#14 15.14 npm warn deprecated @babel/plugin-proposal-object-rest-spread@7.20.7: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-object-rest-spread instead.
2025-Jul-27 02:22:21.668189
#14 16.33 npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
2025-Jul-27 02:22:21.873802
#14 16.54 npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
2025-Jul-27 02:22:22.118084
#14 16.63 npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
2025-Jul-27 02:23:29.069627
#14 83.73
2025-Jul-27 02:23:29.069627
#14 83.73 added 974 packages, and audited 977 packages in 1m
2025-Jul-27 02:23:29.236408
#14 83.73
2025-Jul-27 02:23:29.236408
#14 83.74 134 packages are looking for funding
2025-Jul-27 02:23:29.236408
#14 83.74   run `npm fund` for details
2025-Jul-27 02:23:29.236408
#14 83.75
2025-Jul-27 02:23:29.236408
#14 83.75 7 vulnerabilities (3 low, 2 moderate, 2 critical)
2025-Jul-27 02:23:29.236408
#14 83.75
2025-Jul-27 02:23:29.236408
#14 83.75 To address issues that do not require attention, run:
2025-Jul-27 02:23:29.236408
#14 83.75   npm audit fix
2025-Jul-27 02:23:29.259353
#14 83.75
2025-Jul-27 02:23:29.259353
#14 83.75 Some issues need review, and may require choosing
2025-Jul-27 02:23:29.259353
#14 83.75 a different dependency.
2025-Jul-27 02:23:29.259353
#14 83.75
2025-Jul-27 02:23:29.259353
#14 83.75 Run `npm audit` for details.
2025-Jul-27 02:23:29.259353
#14 83.75 npm notice
2025-Jul-27 02:23:29.259353
#14 83.75 npm notice New major version of npm available! 10.8.2 -> 11.5.1
2025-Jul-27 02:23:29.259353
#14 83.75 npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.5.1
2025-Jul-27 02:23:29.259353
#14 83.75 npm notice To update run: npm install -g npm@11.5.1
2025-Jul-27 02:23:29.259353
#14 83.75 npm notice
2025-Jul-27 02:23:29.295787
#14 DONE 84.0s
2025-Jul-27 02:23:29.459878
#15 [11/13] COPY . /app/.
2025-Jul-27 02:23:34.969984
#15 DONE 5.7s
2025-Jul-27 02:23:35.123155
#16 [12/13] RUN  npm run prod
2025-Jul-27 02:23:35.343193
#16 0.373
2025-Jul-27 02:23:35.343193
#16 0.373 > prod
2025-Jul-27 02:23:35.343193
#16 0.373 > echo 'Using pre-compiled assets from public/themes - skipping build'
2025-Jul-27 02:23:35.343193
#16 0.373
2025-Jul-27 02:23:35.531890
#16 0.385 Using pre-compiled assets from public/themes - skipping build
2025-Jul-27 02:23:35.531890
#16 DONE 0.4s
2025-Jul-27 02:23:35.531890
2025-Jul-27 02:23:35.531890
#17 [13/13] COPY . /app
2025-Jul-27 02:23:40.542329
#17 DONE 5.2s
2025-Jul-27 02:23:40.704115
#18 exporting to image
2025-Jul-27 02:23:40.704115
#18 exporting layers
2025-Jul-27 02:24:12.547703
#18 exporting layers 32.0s done
2025-Jul-27 02:24:12.547703
#18 writing image sha256:0bbee8d6d003131b49a1b604f631cc2ca74fe39c51d4d95fe0dde6ec532873e2
2025-Jul-27 02:24:12.631144
#18 writing image sha256:0bbee8d6d003131b49a1b604f631cc2ca74fe39c51d4d95fe0dde6ec532873e2 done
2025-Jul-27 02:24:12.646324
#18 naming to docker.io/library/s888kgk480ww8s0w88o0gwso:98de18cabf09af55bfa61ea711c866ec36fe71d1 done
2025-Jul-27 02:24:12.646324
#18 DONE 32.0s
2025-Jul-27 02:24:12.873566
Building docker image completed.
2025-Jul-27 02:24:12.890587
----------------------------------------
2025-Jul-27 02:24:12.900737
Rolling update started.
2025-Jul-27 02:24:13.270522
[CMD]: docker exec nco4cg0oo8go8og80c0wkg4o bash -c 'SOURCE_COMMIT=98de18cabf09af55bfa61ea711c866ec36fe71d1 COOLIFY_URL=http://s888kgk480ww8s0w88o0gwso.************.sslip.io COOLIFY_FQDN=s888kgk480ww8s0w88o0gwso.************.sslip.io COOLIFY_BRANCH=features/rentals  docker compose --project-name s888kgk480ww8s0w88o0gwso --project-directory /artifacts/nco4cg0oo8go8og80c0wkg4o -f /artifacts/nco4cg0oo8go8og80c0wkg4o/docker-compose.yaml up --build -d'
2025-Jul-27 02:24:13.270522
Container s888kgk480ww8s0w88o0gwso-021904276222  Creating
2025-Jul-27 02:24:13.428289
s888kgk480ww8s0w88o0gwso-021904276222 Your kernel does not support memory swappiness capabilities or the cgroup is not mounted. Memory swappiness discarded.
2025-Jul-27 02:24:13.472445
Container s888kgk480ww8s0w88o0gwso-021904276222  Created
2025-Jul-27 02:24:13.472445
Container s888kgk480ww8s0w88o0gwso-021904276222  Starting
2025-Jul-27 02:24:13.763988
Container s888kgk480ww8s0w88o0gwso-021904276222  Started
2025-Jul-27 02:24:13.810540
New container started.
2025-Jul-27 02:24:13.843711
Removing old containers.
2025-Jul-27 02:24:14.101377
Rolling update completed.
2025-Jul-27 02:24:15.153560
Gracefully shutting down build container: nco4cg0oo8go8og80c0wkg4o
2025-Jul-27 02:24:15.674354
[CMD]: docker stop --time=30 nco4cg0oo8go8og80c0wkg4o
2025-Jul-27 02:24:15.674354
nco4cg0oo8go8og80c0wkg4o
2025-Jul-27 02:24:16.018016
[CMD]: docker rm -f nco4cg0oo8go8og80c0wkg4o
2025-Jul-27 02:24:16.018016
Error response from daemon: removal of container nco4cg0oo8go8og80c0wkg4o is already in progress