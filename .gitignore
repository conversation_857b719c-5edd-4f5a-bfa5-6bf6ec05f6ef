# ===================================
# PRODUCTION-OPTIMIZED .GITIGNORE
# For Coolify Docker Deployment
# ===================================

# ===================================
# DEVELOPMENT DEPENDENCIES
# ===================================
/node_modules
/bower_components
yarn.lock
bun.lockb
package-lock.json

# Include composer.lock for consistent deployments
# composer.lock

# Include package-lock.json for consistent Node.js builds
!package-lock.json

# ===================================
# ENVIRONMENT & CONFIGURATION
# ===================================
.env
.env.backup
.env.testing
.env.local
.env.production

# Keep .env.example for deployment reference
!.env.example

# ===================================
# IDE & EDITOR FILES
# ===================================
/.idea
/.vscode
/.vagrant
/.phpintel
.phpstorm.meta.php
.phpactor.json
sftp-config.json

# ===================================
# OPERATING SYSTEM FILES
# ===================================
.DS_Store
Thumbs.db
__MACOSX

# ===================================
# LARAVEL FRAMEWORK FILES
# ===================================
/vendor
/storage/*.key
/storage/app/backup
/storage/cache_keys.json
/storage/.license
/storage/fonts
/storage/framework/*.txt
/storage/framework/laravel-excel/
/storage/installing
/storage/installed
storage/framework/maintenance.php

# ===================================
# BUILD & DEVELOPMENT ARTIFACTS
# ===================================
/public/hot
/.sass-cache
/.scribe

# ===================================
# TESTING & DEVELOPMENT TOOLS
# ===================================
.phpunit.result.cache
_ide_helper.php
_ide_helper_models.php
npm-debug.log
Homestead.json
Homestead.yaml
/.php_cs.cache

# ===================================
# LOGS & TEMPORARY FILES
# ===================================
/log
/storage/logs/*.log

# ===================================
# COOLIFY DEPLOYMENT FILES - INCLUDED
# ===================================
# The following are INCLUDED for Coolify deployment:
# - Dockerfile (production container configuration)
# - nginx.conf (web server configuration)
# - php.ini (PHP runtime configuration)
# - .dockerignore (build optimization)
# - COOLIFY-DEPLOYMENT.md (deployment guide)
# - public/themes (frontend theme assets)
# - public/vendor/core (Botble core assets)
# - All language files (multilingual support)

# ===================================
# STORAGE SYMLINK (EXCLUDED)
# ===================================
# This will be created during deployment
/public/storage

# ===================================
# CUSTOM EXCLUSIONS
# ===================================
# Add any project-specific files to exclude below:

# Database files (local test databases)
*.sqlite
database.sql
database_export_*.sql

# Removed deployment files (cleaned up)
docker-compose.yml
docker-dev.sh
deploy.php
deploy.sh
deploy-config.php
test-deployment.sh
nixpacks.toml
deployment_files/
docker/
coolify*.log
cookies.txt

# Test files and directories
/tests/
phpunit.xml

# ===================================
# ESSENTIAL COOLIFY FILES - FORCE INCLUDE
# ===================================
# Ensure these files are always included for deployment
!Dockerfile
!nginx.conf
!php.ini
!.dockerignore
!COOLIFY-DEPLOYMENT.md

# ===================================
# DEPLOYMENT LOGS & SENSITIVE FILES
# ===================================
.env.production
.env.staging
deployment_logs/
