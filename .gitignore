# ===================================
# PRODUCTION-OPTIMIZED .GITIGNORE
# For Coolify Docker Deployment
# ===================================

# ===================================
# DEVELOPMENT DEPENDENCIES
# ===================================
/node_modules
/bower_components
yarn.lock
bun.lockb
# package-lock.json - Include for consistent Node.js builds

# Include composer.lock for consistent deployments
# composer.lock

# ===================================
# ENVIRONMENT & CONFIGURATION
# ===================================
.env
.env.backup
.env.testing
.env.local
.env.production

# Keep .env.example for deployment reference
!.env.example

# ===================================
# IDE & EDITOR FILES
# ===================================
/.idea
/.vscode
/.vagrant
/.phpintel
.phpstorm.meta.php
.phpactor.json
sftp-config.json

# ===================================
# OPERATING SYSTEM FILES
# ===================================
.DS_Store
Thumbs.db
__MACOSX

# ===================================
# LARAVEL DEPENDENCIES & RUNTIME FILES
# ===================================
# Composer dependencies (installed during build)
/vendor

# Runtime storage files (generated during runtime)
/storage/*.key
/storage/logs/*.log
/storage/framework/cache/data/*
/storage/framework/sessions/*
/storage/framework/views/*
/storage/framework/testing/*

# Keep storage structure but exclude content
!/storage/framework/cache/data/.gitignore
!/storage/framework/sessions/.gitignore
!/storage/framework/views/.gitignore
!/storage/framework/testing/.gitignore
!/storage/logs/.gitignore

# Botble-specific runtime files
/storage/cache_keys.json
/storage/installing
/storage/installed

# ===================================
# BUILD ARTIFACTS (DEVELOPMENT ONLY)
# ===================================
# Hot reload files (development)
/public/hot

# Development build artifacts
/.sass-cache
/.scribe

# Public storage symlink (created during deployment)
/public/storage

# ===================================
# DEVELOPMENT & TESTING TOOLS
# ===================================
# PHPUnit cache and results
.phpunit.result.cache
/tests/
phpunit.xml

# IDE helper files (Laravel IDE Helper package)
_ide_helper.php
_ide_helper_models.php

# Development tools cache
/.php_cs.cache
.php-cs-fixer.cache

# Local development environment
Homestead.json
Homestead.yaml
npm-debug.log

# ===================================
# COOLIFY DEPLOYMENT FILES - INCLUDED
# ===================================
# The following are INCLUDED for Coolify deployment:
# - Dockerfile (production container configuration)
# - nginx.conf (web server configuration)
# - php.ini (PHP runtime configuration)
# - .dockerignore (build optimization)
# - COOLIFY-DEPLOYMENT.md (deployment guide)
# - public/themes (frontend theme assets)
# - public/vendor/core (Botble core assets)
# - All language files (multilingual support)

# ===================================
# LOCAL DEVELOPMENT & TEST FILES
# ===================================
# Database files (local test databases)
*.sqlite
database.sql
database_export_*.sql

# Removed deployment files (cleaned up)
docker-compose.yml
docker-dev.sh
deploy.php
deploy.sh
deploy-config.php
test-deployment.sh
nixpacks.toml
deployment_files/
docker/
coolify*.log
cookies.txt

# ===================================
# ESSENTIAL COOLIFY FILES - FORCE INCLUDE
# ===================================
# Ensure these files are always included for deployment
!Dockerfile
!nginx.conf
!php.ini
!.dockerignore
!COOLIFY-DEPLOYMENT.md

# ===================================
# DEPLOYMENT LOGS & SENSITIVE FILES
# ===================================
.env.production
.env.staging
deployment_logs/
