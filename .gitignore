# Laravel & Botble CMS - Minimal .gitignore

# Dependencies
/node_modules
/vendor

# Environment files
.env
.env.backup
.env.testing
.env.local
.env.production

# Runtime storage
/storage/*.key
/storage/logs/*.log
/storage/framework/cache/data/*
/storage/framework/sessions/*
/storage/framework/views/*
/storage/framework/testing/*

# Build artifacts
/public/hot
/public/storage

# Development tools
.phpunit.result.cache
_ide_helper.php
_ide_helper_models.php
/.php_cs.cache
.php-cs-fixer.cache

# IDE & OS
/.idea
/.vscode
.DS_Store
Thumbs.db

# Local development
Homestead.json
Homestead.yaml
npm-debug.log

# Local files
*.sqlite
*.log
